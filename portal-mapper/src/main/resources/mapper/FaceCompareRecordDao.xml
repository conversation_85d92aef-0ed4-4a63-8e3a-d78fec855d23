<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.face.FaceCompareRecordDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="quxing.data.portal.model.face.FaceCompareRecordEntity" id="faceCompareRecordMap">
        <result property="id" column="id"/>
        <result property="guildAnchorId" column="guild_anchor_id"/>
        <result property="wholeAnchorId" column="whole_anchor_id"/>
        <result property="similarity" column="similarity"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    <update id="updateByGuild">
        UPDATE face_compare_record
        <set>
            is_read = 1
        </set>
        <where>
            whole_anchor_id IN (
            SELECT wab.anchor_id FROM whole_anchor_base AS wab
            INNER JOIN guild_anchor_base AS gab ON gab.tiktok_id = wab.tik_tok_id
            )
        </where>
    </update>
    <update id="updateByAnchor">
        UPDATE face_compare_record
        <set>
            is_trumpet = #{trumpetFlag}
        </set>
        <where>
            is_trumpet = 0
            AND (guild_anchor_id, whole_anchor_id) IN
            <foreach collection="faceTrumpetAnchorList" item="i" open="(" separator="," close=")">
                (#{i.guildAnchorId}, #{i.wholeAnchorId})
            </foreach>
        </where>
    </update>

    <select id="selectCountAndMaxByGuild"
            resultType="quxing.data.portal.model.face.resp.FaceCompareCountAndMaxResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        fcr.whole_anchor_id,
        MAX(fcr.id) AS maxId,
        COUNT(fcr.id) AS countNum,
        SUM(IF(fcr.is_trumpet = 0, 1, 0)) AS noReadNum
        FROM face_compare_record AS fcr
        LEFT JOIN anchor_base_info AS gab ON gab.anchor_id = fcr.whole_anchor_id and is_guild=1
        <where>
            fcr.guild_anchor_id IN
            <foreach collection="guildAnchorIds" item="guildAnchorIdItem" open="(" separator="," close=")">
                #{guildAnchorIdItem}
            </foreach>
            AND fcr.similarity BETWEEN #{similarityBegin} AND #{similarityEnd}
            <if test="isGuild != null and isGuild">
                AND (gab.id IS NOT NULL AND gab.id > 0)
            </if>
            <if test="isGuild != null and !isGuild">
                AND (gab.id IS NULL OR gab.id = 0)
            </if>
            <if test="isTrumpet != null">
                AND fcr.is_trumpet = #{isTrumpet}
            </if>
            <if test="isTrumpet == null">
                AND (fcr.is_trumpet IN (0, 1) OR fcr.is_trumpet IS NULL)
            </if>
        </where>
        GROUP BY fcr.guild_anchor_id
    </select>

    <select id="selectUnreadCountByAgent" resultType="java.lang.Integer">
        SELECT
        COUNT(fcr.id)
        FROM
        face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi1 ON abi1.anchor_id = fcr.guild_anchor_id
        AND abi1.is_guild = 1
        AND abi1.last_live_time >= #{lastLiveTimeBegin}
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <where>
            fcr.is_trumpet = 0
            AND fcr.similarity BETWEEN 90 AND 100
            AND NOT EXISTS ( SELECT abi2.id FROM anchor_base_info AS abi2 WHERE abi2.anchor_id = fcr.whole_anchor_id AND
            abi2.is_guild = 1)
        </where>
    </select>
    <select id="selectExportByGuild" resultType="quxing.data.portal.model.guild.Resp.GuildAnchorLogoutExportResp">
        SELECT
        abi.anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.agent_name AS guildAgentName,
        abi.last_live_time AS guildLastLiveTime,
        abi.total_earning AS guildTotalEarning,
        abi.total_sound_byte AS guildTotalSoundByte,
        wab.anchor_id AS wholeAnchorId,
        CONCAT('https://www.douyin.com/user/', wab.sec_uid) AS wholeHomepage,
        wab.tik_tok_id AS wholeTikTokId,
        wab.tik_tok_num AS wholeTikTokNum,
        wab.nickname AS wholeNickname,
        fcr.similarity AS similarity
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        LEFT JOIN guild_anchor_base AS gab ON gab.tiktok_id = wab.tik_tok_id
        <where>
            fcr.guild_anchor_id IN
            <foreach collection="guildAnchorIds" item="guildAnchorIdItem" open="(" separator="," close=")">
                #{guildAnchorIdItem}
            </foreach>
            AND fcr.similarity BETWEEN #{similarityBegin} AND #{similarityEnd}
            <if test="isReport != null">
                AND fcr.is_report = #{isReport}
            </if>
            <if test="isReport == null">
                AND fcr.is_report IN (0, 1, 2, 3)
            </if>
            <if test="isGuild != null and isGuild">
                AND (gab.id IS NOT NULL AND gab.id > 0)
            </if>
            <if test="isGuild == null or !isGuild">
                AND (gab.id IS NULL OR gab.id = 0)
            </if>
            <if test="isTrumpet != null">
                AND fcr.is_trumpet = #{isTrumpet}
            </if>
            <if test="isTrumpet == null">
                AND (fcr.is_trumpet IN (0, 1) OR fcr.is_trumpet IS NULL)
            </if>
        </where>
    </select>


    <select id="getCompareRecordInfo"
            resultType="quxing.data.portal.model.face.resp.FaceCompareRecordResp">
        SELECT *
        FROM face_compare_record AS re
        <where>
            re.create_time BETWEEN #{bean.startTime} and #{bean.endTime}
            <if test="bean.anchorIds!=null and bean.anchorIds.size>0">
                and re.guild_anchor_id in
                <foreach collection="bean.anchorIds" item="anchorId" open="(" separator="," close=")">
                    #{anchorId}
                </foreach>
            </if>
            <if test="bean.minSimilarity != null">
                and re.similarity >= #{bean.minSimilarity}
            </if>
            <if test="bean.maxSimilarity!=null">
                and #{bean.maxSimilarity}>= re.similarity
            </if>
        </where>
        order by re.id desc
    </select>

    <select id="unreadSmallByAgentIds" resultType="java.lang.Integer">
        SELECT count(1) FROM `face_compare_record` re
        INNER JOIN (
        select anchor_id from anchor_base_info ba WHERE is_guild = 1
        INNER JOIN (
        SELECT tiktok_id FROM guild_anchor_base
        WHERE agent_id IN
        <foreach collection="agentIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        )w ON w.tiktok_id = ba.tik_tok_id) a
        on re.guild_anchor_id = a.anchor_id and re.is_trumpet = 0 and re.similarity >= 90
    </select>


    <select id="notConfirmSmallByAgentIds" resultType="java.lang.Integer">
        SELECT
        COUNT(fcr.id)
        FROM face_compare_record fcr
        INNER JOIN anchor_base_info abi1 ON abi1.guild_anchor_id = fcr.anchor_id
        AND abi1.is_guild = 1
        AND abi1.similarity BETWEEN 90 and 100
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi1.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <where>
            AND fcr.is_trumpet = 1
            AND NOT EXISTS (SELECT abi2.anchor_id FROM anchor_base_info AS abi2 WHERE abi2.anchor_id =
            fcr.whole_anchor_id AND abi2.is_guild = 1)
        </where>
    </select>

    <select id="confirmSmallByTime" resultType="String">
        SELECT re.create_time FROM `face_compare_record` re
        INNER JOIN anchor_base_info ba on re.guild_anchor_id=ba.anchor_id AND ba.is_guild = 1
        and re.is_trumpet=1
        and re.create_time BETWEEN #{startTime} and #{endTime}
        where ba.agent_name IN
        <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
            #{agentItem}
        </foreach>
    </select>

    <select id="syncUnreadCountByAgentName" resultType="quxing.data.portal.model.anchor.Resp.RedisUnreadBean">
        SELECT
        abi1.agent_name,
        COUNT(1) AS count
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi1 ON abi1.anchor_id = fcr.guild_anchor_id
        AND abi1.is_guild = 1
        AND abi1.last_live_time >= #{lastLiveTimeBegin}
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi1.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <where>
            fcr.is_trumpet = 0
            AND fcr.similarity BETWEEN 89 AND 100
            AND NOT EXISTS (SELECT abi2.id FROM anchor_base_info AS abi2 WHERE abi2.anchor_id = fcr.whole_anchor_id AND abi2.is_guild = 1)
            AND fcr.create_time BETWEEN #{compareTimeBegin} AND #{compareTimeEnd}
        </where>
        GROUP BY abi1.agent_name
    </select>

    <select id="selectBySpecial" resultType="quxing.data.portal.model.face.SpecialFaceCompareResp">
        SELECT
        fcr.*,wab.ip_need,wab.tik_tok_id,wab.tik_tok_num,wab.avatar,wab.nickname,
        (case when fcr.guild_anchor_face_url is null then vf.url else fcr.guild_anchor_face_url end) as guildFaceUrl
        FROM
        face_compare_record fcr
        INNER JOIN whole_anchor_base wab ON fcr.whole_anchor_id = wab.anchor_id
        inner join video_face vf on fcr.guild_anchor_id=vf.anchor_id
        where fcr.guild_anchor_id in
        <foreach collection="anchorIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and similarity>=88
    </select>
    <select id="selectListByGuildAndWhole"
            resultType="quxing.data.portal.model.face.resp.FaceCompareRecordResp">
        SELECT
        fcr.*,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        vf.url AS guildAnchorFace,
        wab.tik_tok_id AS wholeTikTokId,
        wab.tik_tok_num AS wholeTikTokNum,
        wab.nickname AS wholeNickname,
        waf.url AS wholeAnchorFace
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id
        INNER JOIN video_face AS vf ON vf.anchor_id = fcr.guild_anchor_id
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        INNER JOIN whole_anchor_face AS waf ON waf.id = fcr.whole_anchor_face_id
        <where>
            fcr.guild_anchor_id = #{guildAnchorId}
            AND fcr.whole_anchor_id = #{wholeAnchorId}
        </where>
    </select>
    <select id="selectSpecialCount" resultType="quxing.data.portal.model.face.resp.FaceCompareSpecialCountResp">
        SELECT
        fcr.guild_anchor_id,
        COUNT(fcr.id) AS all_compare_number,
        COUNT(CASE WHEN abi.ip_need = wab.ip_need THEN 1 ELSE null END) AS all_compare_city_number,
        COUNT(CASE WHEN fcr.create_time >= #{dateFlag} THEN 1 ELSE null END ) AS today_compare_number,
        COUNT(CASE WHEN fcr.create_time >= #{dateFlag} AND abi.ip_need = wab.ip_need THEN 1 ELSE null END) AS today_compare_city_number
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        <where>
            fcr.guild_anchor_id IN
            <foreach collection="anchorIds" item="anchorItem" open="(" separator="," close=")">
                #{anchorItem}
            </foreach>
            AND fcr.similarity >= 88
        </where>
        GROUP BY guild_anchor_id
    </select>
    <select id="selectCountByAnchorId"
            resultType="quxing.data.portal.model.guild.Resp.GuildAnchorFansSimilarCountResp">
        SELECT
        guild_anchor_id AS guildAnchorId,
        COUNT(DISTINCT whole_anchor_id) AS trumpetNum
        FROM face_compare_record AS fcr
        <where>
            fcr.guild_anchor_id IN
            <foreach collection="anchorIds" item="anchorItem" open="(" separator="," close=")">
                #{anchorItem}
            </foreach>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND fcr.similarity >= 88
        </where>
    </select>
    <select id="selectTrumpetList" resultType="quxing.data.portal.model.guild.Resp.GuildBreakTrumpetResp">
        SELECT
        fcr.guild_anchor_id,
        fcr.guild_anchor_face_url,
        fcr.whole_anchor_id,
        waf.url AS wholeAnchorFaceUrl,
        wab.tik_tok_id,
        wab.tik_tok_num,
        wab.nickname,
        wab.avatar,
        fcr.similarity,
        COUNT(fcr.id) AS compareNum
        FROM face_compare_record AS fcr
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        INNER JOIN whole_anchor_face AS waf ON waf.id = fcr.whole_anchor_face_id
        <where>
            fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND fcr.similarity BETWEEN #{beginSimilarity} AND #{endSimilarity}
            AND fcr.guild_anchor_id = #{guildAnchorId}
        </where>
        GROUP BY fcr.whole_anchor_id
    </select>
    <select id="selectCityTrumpetList" resultType="quxing.data.portal.model.guild.Resp.GuildBreakTrumpetResp">
        SELECT
        fcr.guild_anchor_id,
        fcr.guild_anchor_face_url,
        fcr.whole_anchor_id,
        waf.url AS wholeAnchorFaceUrl,
        wab.tik_tok_id,
        wab.tik_tok_num,
        wab.nickname,
        wab.avatar,
        fcr.similarity,
        COUNT(fcr.id) AS compareNum
        FROM face_compare_record AS fcr
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        INNER JOIN whole_anchor_face AS waf ON waf.id = fcr.whole_anchor_face_id
        INNER JOIN guild_break_total AS gbt ON gbt.anchor_id = fcr.guild_anchor_id
        <where>
            fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND fcr.similarity BETWEEN #{beginSimilarity} AND #{endSimilarity}
            AND fcr.guild_anchor_id = #{guildAnchorId}
            AND (gbt.city = wab.city OR gbt.city = wab.location)
        </where>
        GROUP BY fcr.whole_anchor_id
    </select>
    <select id="selectCountByAgent" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
<!--        <if test="guildId != null">-->
<!--            AND abi.guild_id = #{guildId}-->
<!--        </if>-->
        AND abi.last_live_time >= #{lastLiveTimeBegin}
        <where>
            AND fcr.similarity BETWEEN 89 AND 100
            AND fcr.create_time BETWEEN #{startDateTime} AND #{endDateTime}
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
        </where>
    </select>

    <select id="selectAuditCountByAgent" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <if test="guildId != null">
            AND abi.guild_id = #{guildId}
        </if>
        AND abi.last_live_time >= #{lastLiveTimeBegin}
        <where>
            fcr.similarity BETWEEN 89 AND 100
            AND fcr.create_time BETWEEN #{startDateTime} AND #{endDateTime}
            AND fcr.is_trumpet != 0
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
        </where>
    </select>
    <select id="selectTrumpetListByAgentNames"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi
        ON abi.anchor_id = fcr.guild_anchor_id
        AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1 AND guild_id = abi.guild_id)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        LIMIT 10
    </select>
    <select id="selectTrumpetTodayByAgentNames"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr FORCE INDEX (createTimeIndex)
        INNER JOIN anchor_base_info AS abi
        ON abi.anchor_id = fcr.guild_anchor_id
        AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        order by guildTotalEarning DESC
        LIMIT 10
    </select>
    <select id="selectGuildList" resultType="quxing.data.portal.model.face.resp.GuildCompareResp">
        SELECT
        abi1.anchor_id AS minAnchorId,
        abi1.tik_tok_id AS minTikTokId,
        abi1.tik_tok_num AS minTikTokNum,
        abi1.nickname AS minNickname,
        fcr.guild_anchor_face_url AS minFaceUrl,
        abi1.agent_name AS minAgentName,
        abi1.avatar AS minAvatar,
        abi1.sec_uid AS minSecUid,
        abi1.guild_id AS minGuildId,
        gb1.`name` AS minGuildName,
        abi2.anchor_id AS maxAnchorId,
        abi2.tik_tok_id AS maxTikTokId,
        abi2.tik_tok_num AS maxTikTokNum,
        abi2.nickname AS maxNickname,
        abi2.agent_name AS maxAgentName,
        abi2.avatar AS maxAvatar,
        abi2.sec_uid AS maxSecUid,
        waf.url AS maxFaceUrl,
        abi2.guild_id AS maxGuildId,
        gb2.`name` AS maxGuildName,
        fcr.id AS compareId,
        fcr.similarity,
        fcr.create_time
        FROM
        face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi1 ON abi1.anchor_id = fcr.guild_anchor_id AND abi1.is_guild = 1
        <if test="minAnchorVague != null and minAnchorVague != ''">
            AND (
            abi1.anchor_id = #{minAnchorVague}
            OR abi1.tik_tok_id = #{minAnchorVague}
            OR abi1.tik_tok_num LIKE CONCAT('%', #{minAnchorVague}, '%')
            OR abi1.nickname LIKE CONCAT('%', #{minAnchorVague}, '%')
            )
        </if>
        INNER JOIN guild_base AS gb1 ON gb1.id = abi1.guild_id
        INNER JOIN anchor_base_info AS abi2 ON abi2.anchor_id = fcr.whole_anchor_id AND abi2.is_guild = 1
        <if test="maxAnchorVague != null and maxAnchorVague != ''">
            AND (
            abi2.anchor_id = #{maxAnchorVague}
            OR abi2.tik_tok_id = #{maxAnchorVague}
            OR abi2.tik_tok_num LIKE CONCAT('%', #{maxAnchorVague}, '%')
            OR abi2.nickname LIKE CONCAT('%', #{maxAnchorVague}, '%')
            )
        </if>
        INNER JOIN guild_base AS gb2 ON gb2.id = abi2.guild_id
        INNER JOIN whole_anchor_face AS waf ON waf.id = fcr.whole_anchor_face_id
        <where>
            <if test="beginCompareTime != null and beginCompareTime != ''">
                AND fcr.create_time >= #{beginCompareTime}
            </if>
            <if test="endCompareTime != null and endCompareTime != ''">
                AND #{endCompareTime} >= fcr.create_time
            </if>
            <if test="beginSimilarity != null">
                AND fcr.similarity >= #{beginSimilarity}
            </if>
            <if test="endSimilarity != null">
                AND #{endSimilarity} >= fcr.similarity
            </if>
            AND abi1.guild_id != abi2.guild_id
            <if test="guildIdLeft != null and guildIdRight != null">
                AND (abi1.guild_id IN CONCAT(#{guildIdLeft}, #{guildIdRight}) AND abi2.guild_id IN CONCAT(#{guildIdLeft}, #{guildIdRight}))
            </if>
            <if test="guildIdLeft != null and guildIdRight == null">
                AND (abi1.guild_id = #{guildIdLeft} OR abi2.guild_id = #{guildIdLeft})
            </if>
            <if test="guildIdLeft == null and guildIdRight != null">
                AND (abi1.guild_id = #{guildIdRight} OR abi2.guild_id = #{guildIdRight})
            </if>
        </where>
        GROUP BY fcr.guild_anchor_id, fcr.whole_anchor_id
    </select>
    <select id="selectTimeoutTrumpet" resultType="quxing.data.portal.model.face.resp.FaceCompareTimeoutResp">
        SELECT
        fcr.id AS compareId,
        abi.anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.agent_name AS guildAgentName,
        abi.sec_uid AS guildSecUid,
        abi.avatar AS guildAvatar,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        wab.anchor_id AS wholeAnchorId,
        wab.tik_tok_id AS wholeTikTokId,
        wab.tik_tok_num AS wholeTikTokNum,
        wab.nickname AS wholeNickname,
        wab.sec_uid AS wholeSecUid,
        wab.avatar AS wholeAvatar,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity AS similarity,
        fcr.create_time AS compareTime
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1 AND abi.last_live_time >= #{lastLiveTime}
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <if test="agentName != null and agentName != ''">
            AND abi.agent_name LIKE CONCAT('%', #{agentName}, '%')
        </if>
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        <where>
            fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND NOT EXISTS (SELECT id FROM face_trumpet_log WHERE face_compare_id = fcr.id AND #{cutoffTime} >= create_time)
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1 AND guild_id = 1)
            AND fcr.similarity >= 89
        </where>
    </select>
    <select id="selectTrumpet" resultType="quxing.data.portal.model.face.resp.FaceCompareTimeoutResp">
        SELECT
        fcr.id AS compareId,
        abi.anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.agent_name AS guildAgentName,
        abi.sec_uid AS guildSecUid,
        abi.avatar AS guildAvatar,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        wab.anchor_id AS wholeAnchorId,
        wab.tik_tok_id AS wholeTikTokId,
        wab.tik_tok_num AS wholeTikTokNum,
        wab.nickname AS wholeNickname,
        wab.sec_uid AS wholeSecUid,
        wab.avatar AS wholeAvatar,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity AS similarity,
        fcr.create_time AS compareTime
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1 AND abi.last_live_time >= #{lastLiveTime}
        <if test="agentNameList != null and agentNameList.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNameList" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        <if test="agentName != null and agentName != ''">
            AND abi.agent_name LIKE CONCAT('%', #{agentName}, '%')
        </if>
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        <where>
            fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            <if test="isNormal != null and isNormal">
                AND EXISTS (SELECT id FROM face_trumpet_log WHERE face_compare_id = fcr.id AND #{cutoffTime} >= create_time)
            </if>
            <if test="isNormal != null and !isNormal">
                AND NOT EXISTS (SELECT id FROM face_trumpet_log WHERE face_compare_id = fcr.id AND #{cutoffTime} >= create_time)
            </if>
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1 AND guild_id = 1)
            AND fcr.similarity >= 89
        </where>
    </select>
    <select id="selectUnifyList" resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id
        <if test="req.guildId != null">
            AND abi.guild_id = #{req.guildId}
        </if>
        <if test="req.guildIds != null and req.guildIds.size > 0">
            and abi.guild_id IN
            <foreach collection="req.guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        <where>
            fcr.type_flag = 1
            <if test="req.beginSimilarity != null">
                AND fcr.similarity >= #{req.beginSimilarity}
            </if>
            <if test="req.endSimilarity != null">
                AND #{req.endSimilarity} >= fcr.similarity
            </if>
            <if test="req.beginDate != null and req.beginDate != ''">
                AND fcr.create_time >= #{req.beginDate}
            </if>
            <if test="req.endDate != null and req.beginDate != ''">
                AND #{req.endDate} >= fcr.create_time
            </if>
        </where>
    </select>
    <select id="selectCountByGuild" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="guildId != null">
            AND abi.guild_id = #{guildId}
        </if>
        AND abi.last_live_time >= #{lastLiveTimeBegin}
        <where>
            fcr.similarity BETWEEN 89 AND 100
            AND fcr.create_time BETWEEN #{startDateTime} AND #{endDateTime}
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND guild_id = abi.guild_id AND is_guild = 1)
        </where>
    </select>
    <select id="selectAuditCountByGuild" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="guildId != null">
            AND abi.guild_id = #{guildId}
        </if>
        AND abi.last_live_time >= #{lastLiveTimeBegin}
        <where>
            fcr.similarity BETWEEN 89 AND 100
            AND fcr.create_time BETWEEN #{startDateTime} AND #{endDateTime}
            AND fcr.is_trumpet != 0
            AND NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND guild_id = abi.guild_id AND is_guild = 1)
        </where>
    </select>
    <select id="selectTrumpetCount" resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetCountResp">
        SELECT abi.guild_id AS guildId, COUNT(fcr.id) AS trumpetCount
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <where>
            fcr.similarity >= 89
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
        </where>
        GROUP BY abi.guild_id
    </select>
    <select id="selectCauselessAuditCount" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1 AND abi.guild_id = #{guildId}
        <where>
            fcr.similarity BETWEEN 93 AND 100
            AND fcr.is_trumpet = 2
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND NOT EXISTS (SELECT ftl.id FROM face_trumpet_log AS ftl WHERE ftl.face_compare_id = fcr.id AND ftl.trumpet_reason IS NULL)
        </where>
    </select>
    <select id="selectTrumpetListByAgentAndGuild"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="guildIds != null and guildIds.size > 0">
            AND abi.guild_id IN
            <foreach collection="guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        AND abi.
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        LIMIT 10
    </select>
    <select id="selectTrumpetTodayByAgentAndGuild"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="guildIds != null and guildIds.size > 0">
            AND abi.guild_id IN
            <foreach collection="guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        order by guildTotalEarning DESC
        LIMIT 10
    </select>
    <select id="selectTodayTrumpetListByAgentAndGuild"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="guildIds != null and guildIds.size > 0">
            AND abi.guild_id IN
            <foreach collection="guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        order by guildTotalEarning DESC
        LIMIT 10
    </select>

    <select id="selectTodayTrumpetListByAgentAndGuildNoLimit"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr FORCE INDEX(createTimeIndex)
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="guildIds != null and guildIds.size > 0">
            AND abi.guild_id IN
            <foreach collection="guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
    </select>

    <select id="selectAuditTrueNumByGuild" resultType="java.lang.Integer">
        SELECT COUNT(fcr.id)
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1 AND abi.guild_id = #{guildId}
        <where>
            fcr.similarity BETWEEN 89 AND 100
            AND fcr.is_trumpet = 1
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
        </where>
    </select>
    <select id="selectTrumpetListByAgentAndExist"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="guildIds != null and guildIds.size > 0">
            AND abi.guild_id IN
            <foreach collection="guildIds" item="guildItem" open="(" separator="," close=")">
                #{guildItem}
            </foreach>
        </if>
        <if test="existTikTokIds != null and existTikTokIds.size > 0">
            AND abi.tik_tok_id NOT IN
            <foreach collection="existTikTokIds" item="tikTokItem" open="(" separator="," close=")">
                #{tikTokItem}
            </foreach>
        </if>
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        LIMIT 10
    </select>
    <select id="selectTrumpetListByAgentNamesAndExist"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.guild_anchor_id
        AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <if test="existTikTokIds != null and existTikTokIds.size > 0">
            AND abi.tik_tok_id NOT IN
            <foreach collection="existTikTokIds" item="tikTokItem" open="(" separator="," close=")">
                #{tikTokItem}
            </foreach>
        </if>
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1 AND guild_id = abi.guild_id)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        LIMIT 10
    </select>
    <select id="selectTrumpetCountForAnchor"
            resultType="quxing.data.portal.model.guild.Resp.AnchorTrumpetCountResp">
        SELECT
        fcr.guild_anchor_id AS anchorId,
        COUNT(DISTINCT fcr.whole_anchor_id) AS `count`
        FROM face_compare_record AS fcr
        <where>
            fcr.whole_anchor_id > 0
            AND fcr.guild_anchor_id IN
            <foreach collection="anchorIds" item="anchorItem" open="(" separator="," close=")">
                #{anchorItem}
            </foreach>
            <if test="trumpetGuildIds != null and trumpetGuildIds.size > 0">
                AND EXISTS (
                    SELECT abi.id
                    FROM anchor_base_info AS abi
                    WHERE abi.guild_id IN
                    <foreach collection="trumpetGuildIds" item="guildItem" open="(" separator="," close=")">
                        #{guildItem}
                    </foreach>
                    AND abi.anchor_id = fcr.whole_anchor_id
                )
            </if>
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            GROUP BY fcr.guild_anchor_id
        </where>
    </select>
    <select id="selectTrumpetByReq" resultType="quxing.data.portal.model.guild.Resp.GuildAnchorTrumpetResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        fcr.whole_anchor_id AS trumpetAnchorId,
        wab.tik_tok_id AS trumpetTikTokId,
        wab.tik_tok_num AS trumpetTikTokNum,
        wab.nickname AS trumpetNickname,
        wab.avatar AS trumpetAvatar,
        abi.guild_id AS trumpetGuildId,
        abi.agent_name AS trumpetAgentName,
        abi.total_earning AS trumpetTotalEarning,
        wab.sec_uid AS trumpetSecUid,
        abi.ip_need AS trumpetIpNeed,
        waf.url AS trumpetFaceUrl
        FROM face_compare_record AS fcr
        INNER JOIN whole_anchor_base AS wab ON wab.anchor_id = fcr.whole_anchor_id
        INNER JOIN whole_anchor_face AS waf ON waf.id = fcr.whole_anchor_face_id
        LEFT JOIN anchor_base_info AS abi ON abi.anchor_id = fcr.whole_anchor_id
        <where>
            fcr.whole_anchor_id > 0
            <if test="anchorId != null">
                AND fcr.guild_anchor_id = #{anchorId}
            </if>
            <if test="trumpetAnchorId != null">
                AND fcr.whole_anchor_id = #{trumpetAnchorId}
            </if>
            <if test="trumpetGuildIds != null and trumpetGuildIds.size > 0">
                AND abi.guild_id IN
                <foreach collection="trumpetGuildIds" item="guildItem" open="(" separator="," close=")">
                    #{guildItem}
                </foreach>
            </if>
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            <if test="isTrumpet != null">
                <if test="isTrumpet > -1">
                    AND EXISTS (SELECT * FROM trumpet_remark AS tr WHERE tr.anchor_id = fcr.guild_anchor_id AND tr.trumpet_anchor_id = fcr.whole_anchor_id AND tr.is_trumpet = #{isTrumpet})
                </if>
                <if test="0 > isTrumpet">
                    AND NOT EXISTS (SELECT * FROM trumpet_remark AS tr WHERE tr.anchor_id = fcr.guild_anchor_id AND tr.trumpet_anchor_id = fcr.whole_anchor_id AND tr.is_trumpet IN (0, 1))
                </if>
            </if>
            <if test="beginDate != null">
                AND fcr.create_time >= CONCAT(#{beginDate}, " 00:00:00")
            </if>
            <if test="endDate != null">
                AND CONCAT(#{endDate}, " 23:59:59") >= fcr.create_time
            </if>
        </where>
        GROUP BY fcr.whole_anchor_id, fcr.guild_anchor_id
    </select>

    <select id="selectTrumpetTodayByAgentNamesByLimit"
            resultType="quxing.data.portal.model.guild.Resp.GuildTrumpetPageResp">
        SELECT
        fcr.guild_anchor_id AS guildAnchorId,
        abi.tik_tok_id AS guildTikTokId,
        abi.tik_tok_num AS guildTikTokNum,
        abi.nickname AS guildNickname,
        abi.avatar AS guildAvatar,
        abi.sec_uid AS guildSecUid,
        abi.last_live_time AS guildLastLiveTime,
        fcr.guild_anchor_face_url AS guildFaceUrl,
        abi.agent_name AS guildAgentName,
        abi.total_sound_byte AS guildTotalEarning,
        fcr.whole_anchor_id AS wholeAnchorId,
        fcr.whole_anchor_face_id AS wholeFaceId,
        fcr.similarity,
        fcr.create_time AS compareTime,
        1 AS trumpetNum,
        fcr.id AS compareId,
        abi.guild_id AS guildId
        FROM face_compare_record AS fcr FORCE INDEX (createTimeIndex)
        INNER JOIN anchor_base_info AS abi
        ON abi.anchor_id = fcr.guild_anchor_id
        AND abi.is_guild = 1
        <if test="agentNames != null and agentNames.size > 0">
            AND abi.agent_name IN
            <foreach collection="agentNames" item="agentItem" open="(" separator="," close=")">
                #{agentItem}
            </foreach>
        </if>
        AND abi.last_live_time >= #{dateFlag}
        <where>
            NOT EXISTS (SELECT id FROM anchor_base_info WHERE anchor_id = fcr.whole_anchor_id AND is_guild = 1)
            <if test="similarityBegin != null">
                AND fcr.similarity >= #{similarityBegin}
            </if>
            <if test="similarityEnd != null">
                AND #{similarityEnd} >= fcr.similarity
            </if>
            AND fcr.create_time BETWEEN #{beginTime} AND #{endTime}
            AND is_trumpet = 0
        </where>
        GROUP BY fcr.guild_anchor_id
        order by guildTotalEarning DESC
    </select>


</mapper>
