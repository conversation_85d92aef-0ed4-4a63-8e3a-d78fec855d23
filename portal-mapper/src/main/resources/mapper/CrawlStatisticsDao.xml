<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.statistics.CrawlStatisticsDao">


    <select id="getBefore23AllNumber" resultType="java.math.BigDecimal">
        SELECT SUM(`value`) FROM `crawl_statistics`
        where date_flag=#{dateFlag} and hour_flag=23 and type_flag='feedSuccess'
        <if test="cityCode!=null and cityCode!=''">
            and other_flag=#{dateFlag}
        </if>
    </select>
</mapper>