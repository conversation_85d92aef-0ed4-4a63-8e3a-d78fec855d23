<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.videoAuditLog.VideoAuditLogDao">

    <select id="selectPageByReq"
            resultType="quxing.data.portal.model.videoAuditLog.resp.VideoAuditLogPageResp">
        SELECT
            vs.id AS videoId,
            vs.agent_name,
            abi.anchor_id,
            abi.tik_tok_num,
            abi.nickname,
            vs.url,
            vs.create_time AS recordTime,
            val.audit_status,
            val.remark,
            val.create_user,
            val.create_time AS reviewTime
        FROM video_audit_log AS val
        INNER JOIN video_storage AS vs ON vs.id = val.video_id
        <if test="agentName != null and agentName != ''">
            AND vs.agent_name LIKE CONCAT('%', #{agentName}, '%')
        </if>
        <if test="recordTimeBegin != null and recordTimeBegin != '' and recordTimeEnd != null and recordTimeEnd != ''">
            AND vs.create_time BETWEEN #{recordTimeBegin} AND #{recordTimeEnd}
        </if>
        INNER JOIN anchor_base_info AS abi ON abi.anchor_id = vs.anchor_id
        <if test="anchorVague != null and anchorVague != ''">
            AND (abi.anchor_id LIKE CONCAT('%', #{anchorVague}, '%') OR abi.tik_tok_num LIKE CONCAT('%', #{anchorVague}, '%') OR abi.nickname LIKE CONCAT('%', #{anchorVague}, '%'))
        </if>
        <where>
            <if test="auditStatus != null">
                val.audit_status = #{auditStatus}
            </if>
            <if test="createUser != null">
                AND val.create_user LIKE CONCAT('%', #{createUser}, '%')
            </if>
            <if test="reviewTimeBegin != null and reviewTimeEnd != null and reviewTimeBegin != '' and reviewTimeEnd != ''">
                AND val.create_time BETWEEN #{reviewTimeBegin} AND #{reviewTimeEnd}
            </if>
            <if test="remark != null and remark != ''">
                AND val.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
    </select>
</mapper>