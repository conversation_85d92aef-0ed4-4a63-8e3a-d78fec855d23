<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.face.FaceNotCompareEntityDao">


    <select id="synAddToTable" resultType="quxing.data.portal.model.face.FaceNotCompareEntity">
        SELECT ba.agent_name,re.id as face_compare_id FROM `face_compare_record` re
        INNER JOIN anchor_base_info ba ON re.guild_anchor_id = ba.anchor_id and re.create_time&lt;= #{endTime}
        AND re.is_trumpet = 0 AND similarity BETWEEN #{min} and #{max} AND ba.is_guild = 1 and ba.last_live_time BETWEEN
        #{startTime} and now()
        LEFT JOIN anchor_base_info AS abi2 ON abi2.anchor_id = re.whole_anchor_id and abi2.is_guild = 1
        where abi2.anchor_id IS NULL
    </select>


    <insert id="bathInsert">
        insert into face_not_compare (`agent_name`, `face_compare_id`,`date_flag`,`update_time`,`dept_id`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.agentName}, #{item.faceCompareId},#{item.dateFlag},#{item.updateTime},#{item.deptId})
        </foreach>
    </insert>

    <select id="getGuildInfo" resultType="quxing.data.portal.model.face.resp.DeptFaceListResp">
        SELECT
            fcr.id,
            fcr.guild_anchor_id,
            fcr.whole_anchor_id,
            fcr.similarity,
            fcr.is_trumpet,
            fcr.create_time,
            abi.tik_tok_id guildTikTokId,
            abi.tik_tok_num guildTikTokNum,
            abi.nickname guildNickname,
            wab.tik_tok_id wholeTikTokId,
            wab.tik_tok_num wholeTikTokNum,
            wab.nickname wholeNickname,waf.url faceUrl,vf.url guildFaceUrl
        FROM
            `face_not_compare` fnc
        INNER JOIN face_compare_record fcr ON fnc.face_compare_id = fcr.id
        INNER JOIN anchor_base_info abi ON abi.anchor_id = fcr.guild_anchor_id
        INNER JOIN whole_anchor_base wab ON wab.anchor_id = fcr.whole_anchor_id
        INNER JOIN whole_anchor_face waf ON fcr.whole_anchor_face_id = waf.id
        inner join video_face vf on vf.anchor_id=fcr.guild_anchor_id
        WHERE
            is_delete = 0
        AND date_flag = #{bean.dateFlag,jdbcType=VARCHAR}
        AND dept_id = #{bean.deptId}
    </select>
</mapper>