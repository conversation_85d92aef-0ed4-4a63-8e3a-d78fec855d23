<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.anchor.AreaDao">


    <select id="getCityLiveGrab" resultType="quxing.data.portal.model.liveRoom.resp.CityLiveGrabResp">
        SELECT
        a.`name` areaName,
        a.id areaId,
        sum(ser.thread) thrSum,
        (select count(1) from live_grab_date_flag li inner join anchor_base_info ba on li.anchor_id=ba.anchor_id where city=a.name and date_flag=#{dateFlag} ) allNumber,
        (select count(1) from live_grab_date_flag li where city=a.name and is_grab=1 and date_flag=#{dateFlag}) hasNumber
        FROM area a
        INNER JOIN server_area ser ON a.id = ser.area_id
        WHERE
        parent_id = 0
        GROUP BY
        a.id
    </select>

    <select id="getCityNewAnchor" resultType="quxing.data.portal.model.liveRoom.resp.CityNewAnchorResp">
        SELECT
        a.`name` areaName,
        a.id areaId,
        sum(sa.thread) thrSum
        FROM area a
        INNER JOIN server_area AS sa ON a.id = sa.area_id
        <where>
            a.parent_id = 0
        </where>
        GROUP BY
        a.id
    </select>
</mapper>