<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.anchor.AnchorPublicImportDao">

    <insert id="insertOrUpdateBatch">
        INSERT INTO anchor_public_import(
            anchor_id,
            tiktok_id,
            tiktok_num,
            nickname,
            create_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.anchorId},
                #{item.tiktokId},
                #{item.tiktokNum},
                #{item.nickname},
                #{item.createTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            tiktok_num = VALUES(tiktok_num),
            nickname = VALUES(nickname),
            create_time = VALUES(create_time)
    </insert>

    <select id="selectPageList" resultType="quxing.data.portal.model.anchor.resp.AnchorPublicImportPageResp">
        SELECT
            api.id,
            api.anchor_id,
            api.tiktok_id,
            api.tiktok_num,
            api.nickname,
            vf.url,
            api.create_time
        FROM anchor_public_import api
        LEFT JOIN video_face vf ON vf.anchor_id = api.anchor_id
        <where>
            <if test="anchorVague != null and anchorVague != ''">
                AND (
                    api.anchor_id = #{anchorVague}
                    OR api.tiktok_id = #{anchorVague}
                    OR api.tiktok_num LIKE CONCAT('%', #{anchorVague}, '%')
                    OR api.nickname LIKE CONCAT('%', #{anchorVague}, '%')
                )
            </if>
            <if test="tiktokId != null">
                AND api.tiktok_id = #{tiktokId}
            </if>
            <if test="tiktokNum != null and tiktokNum != ''">
                AND api.tiktok_num LIKE CONCAT('%', #{tiktokNum}, '%')
            </if>
            <if test="nickname != null and nickname != ''">
                AND api.nickname LIKE CONCAT('%', #{nickname}, '%')
            </if>
        </where>
        ORDER BY api.create_time DESC
    </select>

</mapper>
