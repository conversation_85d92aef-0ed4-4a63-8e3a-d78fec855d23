<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="quxing.data.portal.mapper.anchor.AnchorPublicImportDao">

    <insert id="insertOrUpdateBatch">
        INSERT INTO anchor_public_import(
            anchor_id,
            tiktok_id,
            tiktok_num,
            nickname,
            create_time
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
                #{item.anchorId},
                #{item.tiktokId},
                #{item.tiktokNum},
                #{item.nickname},
                #{item.createTime}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            tiktok_num = VALUES(tiktok_num),
            nickname = VALUES(nickname),
            create_time = VALUES(create_time)
    </insert>

</mapper>
