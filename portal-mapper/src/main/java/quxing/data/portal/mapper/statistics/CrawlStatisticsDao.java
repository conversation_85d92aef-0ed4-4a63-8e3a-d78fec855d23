package quxing.data.portal.mapper.statistics;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import quxing.data.portal.model.statistics.CrawlStatistics;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 用户特别关心主播对照表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-05 16:31:07
 */
@Mapper
public interface CrawlStatisticsDao extends BaseMapper<CrawlStatistics> {

    BigDecimal getBefore23AllNumber(@Param("dateFlag") LocalDate dateFlag, @Param("cityCode") String cityCode);
}
