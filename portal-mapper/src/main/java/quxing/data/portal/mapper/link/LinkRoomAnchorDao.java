package quxing.data.portal.mapper.link;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import quxing.data.portal.model.common.Resp.AnchorCountResp;
import quxing.data.portal.model.link.LinkRoomAnchorEntity;
import quxing.data.portal.model.link.result.LiveLinkDetailPageResult;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 连线的房间与主播表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-23 16:21:08
 */
@Mapper
public interface LinkRoomAnchorDao extends BaseMapper<LinkRoomAnchorEntity> {

    List<AnchorCountResp> selectCountByAnchor(@Param("anchorIds") List<Long> anchorIds);

    List<AnchorCountResp> selectCountByAnchorAndDate(
            @Param("anchorIds") List<Long> anchorIds,
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime
    );

    List<LiveLinkDetailPageResult> selectPageByAnchor(@Param("anchorId") Long anchorId);

    List<LiveLinkDetailPageResult> selectPageByAnchorAndDate(@Param("anchorId") Long anchorId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);
}
