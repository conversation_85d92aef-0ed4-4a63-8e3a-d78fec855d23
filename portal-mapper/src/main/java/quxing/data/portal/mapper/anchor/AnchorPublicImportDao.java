package quxing.data.portal.mapper.anchor;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import quxing.data.portal.model.anchor.AnchorPublicImportEntity;
import quxing.data.portal.model.anchor.req.AnchorPublicImportPageReq;
import quxing.data.portal.model.anchor.resp.AnchorPublicImportPageResp;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AnchorPublicImportDao extends BaseMapper<AnchorPublicImportEntity> {

    /**
     * 批量插入或更新
     * @param list 数据列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("list") List<AnchorPublicImportEntity> list);

    /**
     * 分页查询列表
     * @param req 查询条件
     * @return 列表数据
     */
    List<AnchorPublicImportPageResp> selectPageList(AnchorPublicImportPageReq req);
}
