package quxing.data.portal.model.face.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceTrumpetReviewPageReq extends SortPageReq {
    /**
     * 审核时间-起
     */
    private String auditTimeBegin;
    /**
     * 审核时间-止
     */
    private String auditTimeEnd;
    /**
     * 是否是小号 1-是小号 2-不是小号
     */
    private Integer isTrumpet;
    /**
     * 复核状态
     */
    private Integer stateFlag;
    /**
     * 是否已批注
     */
    private Integer isComment;

}
