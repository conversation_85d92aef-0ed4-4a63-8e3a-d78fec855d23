package quxing.data.portal.model.guild.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;

/**
 * 公会主播疑似小号的列表传参
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildAnchorFansSimilarReq extends SortPageReq {
    /**
     * 所属经纪人
     */
    private String agentName;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 粉丝团成员重复数范围起
     */
    private Integer numBegin;

    /**
     * 粉丝团成员重复度数范围止
     */
    private Integer numEnd;

    /**
     * 是否有公会主播人脸图片
     */
    private Boolean existGuildFace;

    /**
     * 人脸相似度范围-起
     */
    private BigDecimal similarityBegin;

    /**
     * 人脸相似度范围-止
     */
    private BigDecimal similarityEnd;

    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;
}
