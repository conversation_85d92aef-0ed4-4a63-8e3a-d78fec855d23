package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人脸查询任务表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-10 11:49:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("task_query_face")
public class TaskQueryFaceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 人脸地址
	 */
	private String faceUrl;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 任务状态 0-任务进行中 1-任务完成 2-任务失败
	 */
	private Integer stateFlag;
	/**
	 * 创建人
	 */
	private String createUser;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 完成时间
	 */
	private LocalDateTime completeTime;

}
