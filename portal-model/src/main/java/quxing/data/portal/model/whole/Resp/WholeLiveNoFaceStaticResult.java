package quxing.data.portal.model.whole.Resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class WholeLiveNoFaceStaticResult implements Serializable {
    /**
     * 无人脸数 = 未开始录制数 + 录制失败数 + 正在录制数 + 等待重试数 + 其他录制数 + 未知
     */
    private Integer noFaceNum = 0;

    /**
     * 未开始录制数
     */
    private Integer noRecordNum = 0;
    /**
     * 录制失败数
     */
    private Integer recordFailNum = 0;
    /**
     * 正在录制数
     */
    private Integer recordingNum = 0;
    /**
     * 等待重试数
     */
    private Integer recordWaitNum = 0;
    /**
     * 其他录制数
     */
    private Integer recordOtherNum = 0;
    /**
     * 未知
     */
    private Integer unknownNum = 0;

    /**
     * 失败-视频流失效
     */
    private Integer videoFailNum = 0;
    /**
     * 失败-截图异常
     */
    private Integer screenshotErrorNum = 0;
    /**
     * 失败-重获视频流失败
     */
    private Integer recaptureVideoFailNum = 0;
    /**
     * 失败-视频中无人脸
     */
    private Integer noFaceDetectedNum = 0;

    /**
     * 其他-游戏或音频直播
     */
    private Integer gameOrRadioNum = 0;
    /**
     * 其他-等待上传
     */
    private Integer waitUploadNum = 0;

    /**
     * 最后更新时间
     */
    private Long updateTimeMs = 0L;
}
