package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class LiveRoomResp {

    private LiveRoomRespData data;

    private Integer status_code;

    @Data
    public static class LiveRoomRespData {
        private LiveRoomData room;
    }

    @Data
    public static class LiveRoomData {
        private Integer status;//标识是否正在直播
        private String title;//直播标题
        private Integer create_time;//开播时间
        private Integer finish_time;//停播时间
        private Integer user_count;//当前在线人数
        private LiveRoomCover cover;//封面
        private LiveRoomStream stream_url;//直播流
        private LiveRoomStats stats;//直播基础数据
        private LiveRoomOwner owner;//主播的基础数据
    }

    @Data
    public static class LiveRoomOwner {
        private String nickname;//主播名称
        private LiveRoomOwnerFollow follow_info;//粉丝数据

    }

    @Data
    public static class LiveRoomOwnerFollow {
        private Integer following_count;//关注数
        private Integer follower_count;//粉丝数
    }

    @Data
    public static class LiveRoomCover {
        private List<String> url_list;//直播间封面
    }

    @Data
    public static class LiveRoomStream {
        private LiveRoomStreamInfo flv_pull_url;
    }

    @Data
    public static class LiveRoomStreamInfo {
        private String FULL_HD1;
        private String HD1;
        private String SD1;
    }

    @Data
    public static class LiveRoomStats {
        private Integer total_user;//直播总人数
        private Integer follow_count;//本场粉丝增长
        private LiveRoomUserComposition user_count_composition;//观众来源
    }

    @Data
    public static class LiveRoomUserComposition {
        private BigDecimal city;//同城
        private BigDecimal video_detail;//推荐
        private BigDecimal my_follow;//关注
        private BigDecimal other;//其他
    }
}
