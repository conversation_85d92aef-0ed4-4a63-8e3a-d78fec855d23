package quxing.data.portal.model.whole.Req;

import lombok.*;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 查询全网开播主播列表条件
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorLiveReq extends SortPageReq {
    ///主播信息
    /**
     * 主播id 全匹配
     */
    private Long anchorId;

    /**
     * 原抖音号 全匹配
     */
    private Long tikTokId;

    /**
     * 主播模糊查询条件 现抖音号 昵称
     */
    private String anchorVague;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 主播所属城市 null-查所有
     */
    private String city;

    /**
     * 签名内容
     */
    private String signature;

    /**
     * 年龄范围-起
     */
    private Integer ageBegin;

    /**
     * 年龄范围-止
     */
    private Integer ageEnd;

    /**
     * 出生日期范围-起
     */
    private String birthdayBegin;

    /**
     * 出生日期范围-止
     */
    private String birthdayEnd;

    /**
     * 是否蓝V
     */
    private Integer isEnterpriseVerify;

    /**
     * 是否本月新主播（只根据首次爬取到的时间判断，存在偏差）
     */
    private Boolean isMonthNew;

    private String firstDayOfMonth;

    /**
     * 是否本日新主播（只根据首次爬取到的时间判断，存在偏差）
     */
    private Boolean isDayNew;

    private String firstDayOfDay;

    /**
     * 是否已进本公会
     */
    private Integer isSelfGuild;

    ///直播信息
    /**
     * 开播日期
     */
    private String dateFlag;

    /**
     * 当前直播状态 2-正在直播 4-已关播
     */
    private Integer status;

    /**
     * 直播标题模糊查询条件
     */
    private String title;

    /**
     * 开播地所属城市
     */
    private String location;

    /**
     * 开播时间范围-起 hh:mm:ss
     */
    private String liveCreateTimeBegin;

    private Long liveCreateTimeBeginL;

    /**
     * 开播时间范围-止 hh:mm:ss
     */
    private String liveCreateTimeEnd;

    private Long liveCreateTimeEndL;

    private Long skipNum;
}
