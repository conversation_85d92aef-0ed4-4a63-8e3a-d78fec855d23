package quxing.data.portal.model.strict;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@TableName("stress_condition")
@Accessors(chain = true)
public class StressCondition {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String city;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
