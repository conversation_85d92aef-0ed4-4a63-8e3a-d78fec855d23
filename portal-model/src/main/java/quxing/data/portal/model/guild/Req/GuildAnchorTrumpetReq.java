package quxing.data.portal.model.guild.Req;

import lombok.Getter;
import lombok.Setter;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
public class GuildAnchorTrumpetReq extends SortPageReq {
    /**
     * 公会主播id
     */
    private Long anchorId;
    /**
     * 小号主播id
     */
    private Long trumpetAnchorId;
    /**
     * 小号所在公会id
     */
    private List<Integer> trumpetGuildIds;
    /**
     * 相似度范围-起
     */
    private BigDecimal similarityBegin;
    /**
     * 相似度范围-止
     */
    private BigDecimal similarityEnd;
    /**
     * 是否是小号
     */
    private Integer isTrumpet;
    /**
     * 对比时间范围-起
     */
    private String beginDate;
    /**
     * 对比时间范围-止
     */
    private String endDate;
}
