package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * 粉丝打赏情况查询条件
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeFansRewardReq extends SortPageReq {

    /**
     * 粉丝id
     */
    private Long fansId;

    /**
     * 开始日期
     */
    private String beginDate;

    /**
     * 结束日期
     */
    private String endDate;
}
