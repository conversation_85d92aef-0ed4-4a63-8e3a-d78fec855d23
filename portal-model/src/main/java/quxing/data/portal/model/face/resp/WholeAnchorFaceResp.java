package quxing.data.portal.model.face.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WholeAnchorFaceResp {
    /**
     * 人脸主键
     */
    private Integer id;
    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 主播昵称
     */
    private String nickname;

    /**
     * 首页地址
     */
    private String homePage;

    /**
     * 地址
     */
    private String url;
    /**
     * 性别
     */
    private Integer gender;

    /**
     * 是否已提取人脸特征
     */
    private Integer isGet;
    /**
     * 截图时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 特征提取时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private Integer age;
}
