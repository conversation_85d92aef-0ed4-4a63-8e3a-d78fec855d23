package quxing.data.portal.model.guild.Resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GuildAnchorLogoutExportResp {
    /**
     * 公会主播-主播id
     */
    @Excel(name = "公会主播id")
    private Long guildAnchorId;
    /**
     * 公会主播-原抖音号
     */
    @Excel(name = "公会主播原抖音号")
    private Long guildTikTokId;
    /**
     * 公会主播-现抖音号
     */
    @Excel(name = "公会主播现抖音号")
    private String guildTikTokNum;
    /**
     * 公会主播-主播昵称
     */
    @Excel(name = "公会主播昵称")
    private String guildNickname;
    /**
     * 公会主播-所属经纪人
     */
    @Excel(name = "公会主播经纪人")
    private String guildAgentName;
    /**
     * 公会主播-最后直播时间
     */
    @Excel(name = "公会主播最后直播时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date guildLastLiveTime;

    /**
     * 公会主播-最后一次直播前30日音浪收入
     */
    @Excel(name = "公会主播30日音浪")
    private Long guildTotalEarning;

    /**
     * 总音浪
     */
    @Excel(name = "总音浪")
    private Long guildTotalSoundByte;

    /**
     * 全网主播-主播id
     */
    @Excel(name = "全网主播id")
    private Long wholeAnchorId;

    /**
     * 全网主播-首页地址
     */
    @Excel(name = "全网主播首页地址")
    private String wholeHomepage;

    /**
     * 全网主播-原抖音号
     */
    @Excel(name = "全网主播原抖音号")
    private Long wholeTikTokId;
    /**
     * 全网主播-现抖音号
     */
    @Excel(name = "全网主播现抖音号")
    private String wholeTikTokNum;
    /**
     * 全网主播-主播昵称
     */
    @Excel(name = "全网主播主播昵称")
    private String wholeNickname;
    /**
     * 人脸相似度
     */
    @Excel(name = "人脸相似度")
    private BigDecimal similarity;
}
