package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公会断播主播统计表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-31 16:06:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("guild_break_total")
public class GuildBreakTotalEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 主播id
	 */
	private Long anchorId;
	/**
	 * 原抖音号
	 */
	private Long tikTokId;
	/**
	 * 现抖音号
	 */
	private String tikTokNum;
	/**
	 * 昵称
	 */
	private String nickname;
	/**
	 * 最后开播时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime lastLiveTime;
	/**
	 * 断播前30天总音浪
	 */
	private Long totalEarning;
	/**
	 * 断播前30天总有效天数
	 */
	private Integer totalDays;
	/**
	 * 断播前30天平均音浪
	 */
	private Long avgEarning;
	/**
	 * 所属城市
	 */
	private String city;
	/**
	 * 所属经纪人
	 */
	private String agentName;
	/**
	 * 是否注销
	 */
	private Integer isLogout;
	/**
	 * 近30日小号数
	 */
	private Integer faceCompareNum30;
	/**
	 * 近30日同城小号数
	 */
	private Integer cityCompareNum30;
	/**
	 * 当日小号数
	 */
	private Integer faceCompareNum;
	/**
	 * 当日同城小号数
	 */
	private Integer cityCompareNum;
	/**
	 * 近30日低相似度小号数
	 */
	private Integer faceCompare30;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

}
