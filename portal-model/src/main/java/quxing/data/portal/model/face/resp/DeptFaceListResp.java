package quxing.data.portal.model.face.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import quxing.data.portal.model.face.FaceTrumpetLogEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class DeptFaceListResp {

    private Integer id;

    /**
     * 工会主播id
     */
    private Long guildAnchorId;

    /**
     * 原抖音号
     */
    private Long guildTikTokId;
    /**
     * 现抖音号
     */
    private String guildTikTokNum;
    /**
     * 昵称
     */
    private String guildNickname;

    /**
     * 全网主播id
     */
    private Long wholeAnchorId;

    private Long wholeTikTokId;

    private String wholeTikTokNum;

    private String wholeNickname;


    /**
     * 全网主播人脸id
     */
    private Integer wholeAnchorFaceId;


    private String guildFaceUrl;

    private String faceUrl;

    /**
     * 相似度
     */
    private BigDecimal similarity;

    /**
     * 是否是小号 0-不确定 1-是小号 2-不是小号
     */
    private Integer isTrumpet;

    /**
     * 创建时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private List<FaceTrumpetLogEntity> logEntityList;
}
