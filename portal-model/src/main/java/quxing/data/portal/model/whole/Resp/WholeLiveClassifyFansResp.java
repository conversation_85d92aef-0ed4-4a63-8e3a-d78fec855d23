package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.liveRoom.WholeLiveEntryEntity;
import quxing.data.portal.model.whole.WholeAnchorBaseEntity;
import quxing.data.portal.model.whole.WholeAnchorLiveEntity;
import quxing.data.portal.model.whole.WholeFansBaseEntity;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeLiveClassifyFansResp extends WholeLiveEntryEntity {

    private WholeAnchorBaseEntity wholeAnchorBase;

    private WholeAnchorLiveEntity wholeAnchorLive;

    private WholeFansBaseEntity wholeFansBase;
}
