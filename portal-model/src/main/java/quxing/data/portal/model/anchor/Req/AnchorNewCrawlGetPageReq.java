package quxing.data.portal.model.anchor.Req;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
public class AnchorNewCrawlGetPageReq extends SortPageReq {
    /**
     * 查询日期
     */
    private String dateFlag;

    /**
     * 对比日期范围-起
     */
    private String beginDate;

    /**
     * 对比日期范围-止
     */
    private String endDate;

    /**
     * 是否是公会主播
     */
    private Boolean isGuild;

    public String getCriteria() {
        String criteria = StrUtil.concat(
                true,
                "dateFlag", this.dateFlag,
                "beginDate", this.beginDate,
                "endDate", this.endDate
        );
        if (this.isGuild == null) {
            criteria += "isGuild";
        }
        else {
            criteria = criteria + "isGuild" + this.isGuild;
        }
        return criteria;
    }
}
