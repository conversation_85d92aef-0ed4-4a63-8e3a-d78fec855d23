package quxing.data.portal.model.head.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class HeadFaceDetailPageParam extends SortPageReq {
    /**
     * 人脸id
     */
    private Long headFaceId;
    /**
     * 对比类型 1-比公会 2-比全网
     */
    private Integer typeFlag;
    /**
     * 相似度范围-起
     */
    private BigDecimal beginSimilarity;
    /**
     * 相似度范围-止
     */
    private BigDecimal endSimilarity;

    /**
     * 经纪人名称
     */
    private String agentName;
}
