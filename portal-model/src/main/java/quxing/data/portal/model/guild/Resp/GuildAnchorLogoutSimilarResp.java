package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 公会已注销主播的疑似小号的列表
 *
 * <AUTHOR>
 */
@Data
public class GuildAnchorLogoutSimilarResp {
    /**
     * 是否已注销
     */
    private Boolean isLogout;
    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long guildAnchorId;
    /**
     * 公会主播首页地址
     */
    private String guildHomePage;
    /**
     * 原抖音号
     */
    private Long guildTikTokId;
    /**
     * 现抖音号
     */
    private String guildTikTokNum;
    /**
     * 主播昵称
     */
    private String guildNickname;
    /**
     * 主播头像
     */
    private String guildAvatar;
    /**
     * 所属部门id
     */
    private Integer guildDepartmentId;
    /**
     * 所属部门名称
     */
    private String guildDepartmentName;
    /**
     * 所属员工id
     */
    private Integer guildEmployeeId;
    /**
     * 所属员工姓名
     */
    private String guildEmployeeName;
    /**
     * 所属经纪人id
     */
    private String guildAgentId;
    /**
     * 所属经纪人
     */
    private String guildAgentName;
    /**
     * 最后直播时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime guildLastLiveTime;
    /**
     * 断播时长
     */
    private Integer interruptDays;
    /**
     * 公会主播人脸
     */
    private String guildFaceUrl;
    /**
     * 公会主播人脸截图时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime guildFaceTime;
    /**
     * 主播视频链接
     */
    private String guildVideoUrl;
    /**
     * 主播视频录制时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime guildVideoCreateTime;
    /**
     * 最后一次直播前30日音浪收入
     */
    private Long guildTotalEarning;

    /**
     * 工会主播ip地址
     */
    private String guildIpStr;
    /**
     * 疑似小号数
     */
    private Integer trumpetNum;

    /**
     * 未读数
     */
    private Integer noReadNum;

    /**
     * 全网主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wholeAnchorId;

    /**
     * 全网主播首页地址
     */
    private String wholeHomepage;

    /**
     * 原抖音号
     */
    private Long wholeTikTokId;
    /**
     * 现抖音号
     */
    private String wholeTikTokNum;
    /**
     * 主播昵称
     */
    private String wholeNickname;
    /**
     * 主播头像
     */
    private String wholeAvatar;

    /**
     * 全网主播信息最后更新
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wholeUpdateTime;


    /**
     * 全网主播人脸
     */
    private String wholeFaceUrl;

    /**
     * 全网主播ip
     */
    private String wholeIpStr;

    /**
     * 全网主播人脸截图时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wholeFaceTime;

    /**
     * 人脸相似度
     */
    private BigDecimal similarity;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 对比时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;

    /**
     * 是否是特别关注
     */
    private Boolean isCare;

    /**
     * 公会主播在OA的id
     */
    private Long guildId;

    /**
     * 是否已在公会
     */
    private Boolean isGuild;

    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;

    /**
     * 是否是小号 0-不确定 1-是小号 2-不是小号
     */
    private Integer isTrumpet;

    /**
     * 小号举报状态 0-未举报 1-已举报待处理 2-平台审核中 3-举报被驳回 4-举报已通过
     */
    private Integer reportStatus;

    /**
     * 相似主播总音浪
     */
    private Long soundByte;

    /**
     * 近2月开播次数
     */
    private Integer recentLiveNum;

    /**
     * 是否是达人
     */
    private Integer isSociable;
}
