package quxing.data.portal.model.guild.Req;

import lombok.Getter;
import lombok.Setter;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
public class GuildTrumpetReq extends SortPageReq {
    /**
     * 公会id
     */
    private List<Integer> guildIds;
    /**
     * 经纪人名称
     */
    private String agentName;
    /**
     * 主播模糊查询条件
     */
    private String anchorVague;
    /**
     * 小号所在公会id
     */
    private List<Integer> trumpetGuildIds;
    /**
     * 相似度范围-起
     */
    private BigDecimal similarityBegin;
    /**
     * 相似度范围-止
     */
    private BigDecimal similarityEnd;
    /**
     * 是否是小号
     */
    private Integer isTrumpet;
}
