package quxing.data.portal.model.guild.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildBreakTotalReq extends SortPageReq {
    /**
     * 部门id
     */
    private Integer departmentId;
    /**
     * 所属经纪人
     */
    private String agentName;
    /**
     * 主播模糊查询条件（主播id，原抖音号，现抖音号，昵称）
     */
    private String anchorVague;
    /**
     * 断播天数范围-起
     */
    private Long beginBreakNum;
    /**
     * 断播天数范围-止
     */
    private Long endBreakNum;

    private LocalDateTime beginTime;
    private LocalDateTime endTime;
    /**
     * 断播前30天日均音浪-起
     */
    private Long beginEarning;
    /**
     * 断播前30天日均音浪-止
     */
    private Long endEarning;
    /**
     * 所属城市
     */
    private String city;
    /**
     * 城市是否为空
     */
    private Boolean cityIsEmpty;
    /**
     * 是否注销
     */
    private Integer isLogout;
    /**
     * 经纪人
     */
    private List<String> agentNames;
    /**
     * 是否有基础人脸
     */
    private Boolean isFace;
    /**
     * 是否有附加人脸
     */
    private Boolean isCareFace;
    /**
     * 是否有优质人脸
     */
    private Boolean existHighFace;
    /**
     * 公会id
     */
    private Integer guildId;

    /**
     * 是否在公会
     */
    private Integer isGuild;
}
