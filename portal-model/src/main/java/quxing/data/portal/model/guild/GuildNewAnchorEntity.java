package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 公会新主播
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-08 17:22:30
 */
@Data
@TableName("guild_new_anchor")
public class GuildNewAnchorEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 原抖音号
	 */
	private Long tikTokId;
	/**
	 * 现抖音号
	 */
	private String tikTokNum;
	/**
	 * 主播昵称
	 */
	private String nickname;
	/**
	 * 入会日期
	 */
	private LocalDate joinDate;
	/**
	 * 是否删除
	 */
	private Integer isDelete;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

}
