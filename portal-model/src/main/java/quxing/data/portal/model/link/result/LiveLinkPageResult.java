package quxing.data.portal.model.link.result;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LiveLinkPageResult {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 首页地址
     */
    private String homePage;

    /**
     * 粉丝数
     */
    private Long mplatformFollowersCount;

    /**
     * 最近开播位置
     */
    private String location;

    /**
     * 连线主播数
     */
    private Integer linkAnchorNum;
}
