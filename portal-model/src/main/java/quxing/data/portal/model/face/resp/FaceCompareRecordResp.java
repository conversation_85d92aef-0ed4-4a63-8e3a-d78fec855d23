package quxing.data.portal.model.face.resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import quxing.data.portal.model.face.FaceCompareRecordEntity;

/**
 * 人脸对比记录返回
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class FaceCompareRecordResp extends FaceCompareRecordEntity {

    /**
     * 原抖音号
     */
    private Long guildTikTokId;
    /**
     * 现抖音号
     */
    private String guildTikTokNum;
    /**
     * 昵称
     */
    private String guildNickname;


    private Long wholeTikTokId;

    private String wholeTikTokNum;

    private String wholeNickname;


    /**
     * 工会主播人脸
     */
    private String guildAnchorFace;

    /**
     * 全网主播人脸
     */
    private String wholeAnchorFace;
}
