package quxing.data.portal.model.face.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuildCompareResp {

    private Long minAnchorId;

    private Long minTikTokId;

    private String minTikTokNum;

    private String minNickname;

    private String minFaceUrl;

    private String minAgentName;

    private String minAvatar;

    private String minSecUid;

    private Integer minGuildId;

    private String minGuildName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime minSigningTime;

    private Long maxAnchorId;

    private Long maxTikTokId;

    private String maxTikTokNum;

    private String maxNickname;

    private String maxAgentName;

    private String maxAvatar;

    private String maxSecUid;

    private String maxFaceUrl;

    private Integer maxGuildId;

    private String maxGuildName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime maxSigningTime;

    private BigDecimal similarity;

    private LocalDateTime createTime;
}
