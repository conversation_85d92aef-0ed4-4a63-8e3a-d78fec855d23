package quxing.data.portal.model.anchor.Resp;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/9/30 13:27
 */
@Data
public class AnchorUnderAgeResult {

    private Integer id;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tiktokId;

    /**
     * 现抖音号
     */
    private String tiktokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 经纪人
     */
    private String agentName;

    /**
     * 员工
     */
    private String empName;

    private Integer empId;

    /**
     * 部门
     */
    private String depName;

    private Integer guildId;

    /**
     * 机构
     */
    private String guildName;

    /**
     * 人脸地址
     */
    private String url;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 年龄审核状态 0-未审核 1-成年 -2未成年
     */
    private Integer ageCheckState;

    /**
     * 年龄审核人
     */
    private String ageCheckUser;

    /**
     * 年龄审核备注
     */
    private String ageCheckRemark;

    /**
     * 年龄审核时间
     */
    private LocalDateTime ageCheckTime;


}
