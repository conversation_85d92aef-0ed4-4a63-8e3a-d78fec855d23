package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 全网主播人脸截图统计
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-08-29 10:32:25
 */
@Data
@TableName("whole_face_cut")
public class WholeFaceCutEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 日期标志
	 */
	private LocalDate dateFlag;
	/**
	 * 截图成功次数
	 */
	private Integer cutFailNum;
	/**
	 * 截图失败次数
	 */
	private Integer cutSuccessNum;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
