package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 公会主播疑似小号的列表返回值
 * <AUTHOR>
 */
@Data
public class GuildAnchorFansSimilarResp {
    /**
     * 主播id
     */
    private Long guildAnchorId;

    /**
     * 原抖音号
     */
    private Long guildTikTokId;

    /**
     * 现抖音号
     */
    private String guildTikTokNum;

    /**
     * 昵称
     */
    private String guildNickname;

    /**
     * 头像
     */
    private String guildAvatar;

    /**
     * 经纪人名称
     */
    private String guildAgentName;

    /**
     * 工会主播人脸照片
     */
    private List<String> guildFaceUrl;

    /**
     * 疑似小号数
     */
    private Integer trumpetNum;

    /**
     * 最后开播时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date guildLastLiveTime;
}
