package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * 根据主播id获取指定主播开播情况传参
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorLivePageReq extends SortPageReq {

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 开播日期范围-起
     */
    private String beginDate;

    /**
     * 开播日期范围-止
     */
    private String endDate;
}
