package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人脸对比记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-03 15:01:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("face_compare_record")
public class FaceCompareRecordEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 工会主播id
     */
    private Long guildAnchorId;

    /**
     * 工会主播人脸图片地址
     */
    private String guildAnchorFaceUrl;

    /**
     * 是否是特别关注记录
     */
    private Boolean guildAnchorIsCare;

    /**
     * 全网主播id
     */
    private Long wholeAnchorId;

    /**
     * 全网主播人脸id
     */
    private Integer wholeAnchorFaceId;
    /**
     * 相似度
     */
    private BigDecimal similarity;
    /**
     * 是否已读
     */
    private Integer isRead;
    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;
    /**
     * 是否是小号 0-不确定 1-是小号 2-不是小号
     */
    private Integer isTrumpet;
    /**
     * 小号判断原因
     */
    private String trumpetReason;
    /**
     * 小号类型 0-自然对比 1-周末统一比
     */
    private Integer typeFlag;
    /**
     * 创建时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
