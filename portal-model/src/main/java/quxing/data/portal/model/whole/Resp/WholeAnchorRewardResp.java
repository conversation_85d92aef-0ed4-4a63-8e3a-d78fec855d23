package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeFansBaseEntity;

import java.time.LocalDate;

/**
 * 粉丝打赏榜列表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorRewardResp extends WholeFansBaseEntity {
    /**
     * 日期
     */
    private LocalDate dateFlag;
    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * 打赏金额
     */
    private Integer score;
    /**
     * 该粉丝今日消费总额（所有消费，包括在其他主播处消费）
     */
    private Integer nowDiamond;
    /**
     * 是否是第一次打赏 1-是 0-不是
     */
    private Integer isFirstGift;
}
