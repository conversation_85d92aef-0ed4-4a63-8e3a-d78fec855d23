package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公会断播主播城市线程数
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-07 15:22:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("guild_break_thread")
public class GuildBreakThreadEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 城市名称
	 */
	private String cityName;
	/**
	 * 城市编号
	 */
	private String cityCode;
	/**
	 * 断播主播数
	 */
	private Integer breakNum;
	/**
	 * 注销主播数
	 */
	private Integer logoutNum;
	/**
	 * 默认线程数
	 */
	private Integer threadNum;
	/**
	 * 自定义线程数（负数为减）
	 */
	private Integer customThreadNum;
	/**
	 * 昨日抓到的直播数据
	 */
	private Integer liveNum;
	/**
	 * 昨日直播的有效人脸数
	 */
	private Integer faceNum;
	/**
	 * 昨日疑似小号数（>=88）
	 */
	private Integer trumpetNum;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime updateTime;

}
