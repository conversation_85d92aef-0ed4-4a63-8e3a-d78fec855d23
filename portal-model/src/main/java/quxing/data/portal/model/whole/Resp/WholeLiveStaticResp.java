package quxing.data.portal.model.whole.Resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WholeLiveStaticResp {
    /**
     * 日期标志
     */
    private String dateFlag;

    /**
     * 弹幕数
     */
    private Integer messageCount;

    /**
     * 打赏次数
     */
    private Long rewardCount;

    /**
     * 打赏物品数
     */
    private Integer rewardNum;

    /**
     * 打赏物品总价 单位：音浪/抖币
     */
    private Long rewardTotalPrice;
}
