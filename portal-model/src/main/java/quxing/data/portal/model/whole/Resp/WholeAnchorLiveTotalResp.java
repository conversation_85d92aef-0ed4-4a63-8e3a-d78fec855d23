package quxing.data.portal.model.whole.Resp;

import lombok.Data;

import java.io.Serializable;

/**
 * 直播统计返回值
 * <AUTHOR>
 */
@Data
public class WholeAnchorLiveTotalResp implements Serializable {
    /**
     * 主播数
     */
    private Integer anchorNum = 0;
    /**
     * 有人脸主播数
     */
    private Integer existFaceNum = 0;
    /**
     * 无人脸主播数
     */
    private Integer noFaceNum = 0;
    /**
     * 今日新截人脸主播数
     */
    private Integer newFaceNum = 0;
    /**
     * 今日新增主播
     */
    private Integer todayAnchorNum = 0;
    /**
     * 今日新增开播有人脸主播数
     */
    private Integer todayExistFaceNum = 0;
    /**
     * 今日新增开播无人脸主播数
     */
    private Integer todayNoFaceNum = 0;
    /**
     * 最后更新时间戳
     */
    private Long lastUpdateTimeMs;

    /**
     * 作品人脸数
     */
    private Integer awemeFaceNum;
}
