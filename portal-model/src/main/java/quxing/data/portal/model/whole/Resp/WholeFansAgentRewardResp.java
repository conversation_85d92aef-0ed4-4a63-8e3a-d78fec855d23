package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class WholeFansAgentRewardResp {

    private Long fansId;

    private Long tikTokId;

    private String tikTokNum;

    private String nickname;

    private String avatar;

    private String city;

    private String agentName;

    /**
     * 全网打赏金额
     */
    private BigDecimal allRewardTotal;

    private BigDecimal gains = BigDecimal.ZERO;

    private Long guildRewardTotal;


    private BigDecimal nowRewardTotal;

    private BigDecimal nowGains = BigDecimal.ZERO;

    private Long nowGuildRewardTotal;

    private BigDecimal finalGains;
}
