package quxing.data.portal.model.whole.Resp;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WholeAnchorIncomeRankResp {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 首页
     */
    private String homePage;

    /**
     * 是否是公会主播
     */
    private Integer isGuild;

    /**
     * 打赏来源粉丝数
     */
    private Long fansTotal;

    /**
     * 礼物数
     */
    private Long rewardNum;

    /**
     * 总金额
     */
    private Long rewardTotal;
}
