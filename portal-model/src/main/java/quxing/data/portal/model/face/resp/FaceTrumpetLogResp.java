package quxing.data.portal.model.face.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.face.FaceTrumpetLogEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceTrumpetLogResp extends FaceTrumpetLogEntity {
    /**
     * 公会主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(name = "公会主播id")
    private Long guildAnchorId;

    /**
     * 公会主播原抖音号
     */
    @Excel(name = "公会-原抖音号")
    private String guildTikTokNum;

    /**
     * 公会主播现抖音号
     */
    @Excel(name = "公会-现抖音号")
    private Long guildTikTokId;

    /**
     * 公会主播昵称
     */
    @Excel(name = "公会-昵称")
    private String guildNickname;

    /**
     * 公会主播首页
     */
    @Excel(name = "公会-首页")
    private String guildHomepage;

    /**
     * 工会主播人脸图片地址
     */
    @Excel(name = "公会-人脸")
    private String guildFaceUrl;

    /**
     * 全网主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Excel(name = "全网主播id")
    private Long wholeAnchorId;

    /**
     * 工会主播原抖音号
     */
    @Excel(name = "全网-原抖音号")
    private String wholeTikTokNum;

    /**
     * 工会主播现抖音号
     */
    @Excel(name = "全网-现抖音号")
    private Long wholeTikTokId;

    /**
     * 工会主播昵称
     */
    @Excel(name = "全网-昵称")
    private String wholeNickname;

    /**
     * 公会主播首页
     */
    @Excel(name = "全网-首页")
    private String wholeHomepage;

    /**
     * 全网主播人脸地址
     */
    @Excel(name = "全网-人脸")
    private String wholeFaceUrl;

    /**
     * 相似度
     */
    @Excel(name = "相似度")
    private BigDecimal similarity;

    /**
     * 对比时间
     */
    @Excel(name = "对比时间")
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;

    /**
     * 审核人姓名
     */
    @Excel(name = "审核人姓名")
    private String auditName;

    /**
     * 部长是否已批注
     */
    private Integer isComment;

    /**
     * 批注信息
     */
    private FaceTrumpetCommentResp faceTrumpetComment;
}
