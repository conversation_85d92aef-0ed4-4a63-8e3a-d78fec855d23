package quxing.data.portal.model.whole.Resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WholeLiveRankPageResp {

    @Excel(name = "排名")
    private Integer rankFlag;

    @Excel(name = "主播id")
    private Long anchorId;

    @Excel(name = "现抖音号")
    private String tikTokNum;

    @Excel(name = "昵称")
    private String nickname;
    /**
     * 主播密文
     */
    private String secUid;

    @Excel(name = "首页地址")
    private String homePage;

    @Excel(name = "人脸图片")
    private String faceUrl;
    /**
     * 主播人脸性别标志
     */
    private Integer gender;

    @Excel(name = "人脸性别")
    private String genderStr;

    @Excel(name = "人脸年龄")
    private Integer age;

    @Excel(name = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
