package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeLiveMessageEntity;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeLiveMessageResp extends WholeLiveMessageEntity {

    /**
     * 主播昵称
     */
    private String anchorNickname;

    /**
     * 主播首页
     */
    private String anchorHomePage;

    /**
     * 主播直播间标题
     */
    private String anchorTitle;

    /**
     * 大哥原抖音号
     */
    private Long fansTikTokId;

    /**
     * 大哥现抖音号
     */
    private String fansTikTokNum;

    /**
     * 大哥昵称
     */
    private String fansNickname;

    /**
     * 大哥首页地址
     */
    private String fansHomePage;
}
