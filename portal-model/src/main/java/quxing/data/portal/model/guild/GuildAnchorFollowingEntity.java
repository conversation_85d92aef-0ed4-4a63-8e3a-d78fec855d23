package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 主播关注表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-31 15:02:02
 */
@Data
@TableName("guild_anchor_following")
public class GuildAnchorFollowingEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 关注人id
	 */
	private Long targetAnchorId;
	/**
	 * 被关注人id
	 */
	private Long anchorId;
	/**
	 * 被关注人原抖音号
	 */
	private Long tikTokId;
	/**
	 * 被关注人现抖音号
	 */
	private String tikTokNum;
	/**
	 * 被关注人昵称
	 */
	private String nickname;
	/**
	 * 被关注人加密id
	 */
	private String secUid;
	/**
	 * 被关注人城市
	 */
	private String city;
	/**
	 * 被关注人头像
	 */
	private String avatar;
	/**
	 * 1-关注 2-关注的关注
	 */
	private Integer type;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

}
