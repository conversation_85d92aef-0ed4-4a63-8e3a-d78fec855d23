package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 人脸小号复核记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-28 15:47:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("face_trumpet_review")
public class FaceTrumpetReviewEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 记录id
	 */
	private Long logId;
	/**
	 * 状态
	 */
	private Integer stateFlag;
	/**
	 * 原因
	 */
	private String reason;
	/**
	 * 创建用户名
	 */
	private String createUsername;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

}
