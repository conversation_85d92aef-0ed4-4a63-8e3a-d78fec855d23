package quxing.data.portal.model.guild.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.beans.BeanInfo;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildAnchorFansSimilarDetailReq extends SortPageReq {

    /**
     * 公会主播id
     */
    private Long guildAnchorId;

    /**
     * 粉丝团成员重复数范围起
     */
    private Integer numBegin;

    /**
     * 粉丝团成员重复数范围止
     */
    private Integer numEnd;

    /**
     * 是否有全网主播人脸图片
     */
    private Boolean existWholeFace;

    /**
     * 人脸相似度范围-起
     */
    private BigDecimal similarityBegin;

    /**
     * 人脸相似度范围-止
     */
    private BigDecimal similarityEnd;

    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;
}
