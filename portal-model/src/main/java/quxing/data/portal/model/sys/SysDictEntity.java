package quxing.data.portal.model.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;

/**
 * 系统字典表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-09 17:13:00
 */
@Data
@TableName("sys_dict")
public class SysDictEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 字典类型
     */
    private String type;
    /**
     * 字典码
     */
    private String code;
    /**
     * 字典名
     */
    private String name;
    /**
     * 备注
     */
    private String desc;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 状态  1-有效  0-无效
     */
    private Integer status;

}
