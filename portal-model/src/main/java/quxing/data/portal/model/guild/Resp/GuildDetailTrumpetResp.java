package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
public class GuildDetailTrumpetResp {
    /**
     * 主键
     */
    private Long id;
    /**
     * 工会主播id
     */
    private Long guildAnchorId;
    /**
     * 工会主播人脸
     */
    private String guildAnchorFaceUrl;
    /**
     * 全网主播id
     */
    private Long wholeAnchorId;
    /**
     * 全网主播人脸
     */
    private String wholeAnchorFaceUrl;
    /**
     * 相似度
     */
    private BigDecimal similarity;
    /**
     * 对比时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;
}
