package quxing.data.portal.model.link.param;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveLinkPageParam extends SortPageReq {
    /**
     * 直播日期
     */
    private String dateFlag;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 性别
     */
    private Integer gender;
}
