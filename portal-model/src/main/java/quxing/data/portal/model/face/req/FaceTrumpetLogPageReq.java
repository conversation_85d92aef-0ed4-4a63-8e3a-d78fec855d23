package quxing.data.portal.model.face.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceTrumpetLogPageReq extends SortPageReq {
    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 公会主播查询条件
     */
    private String guildAnchorVague;

    /**
     * 对比时间范围-起
     */
    private String compareTimeBegin;
    /**
     * 对比时间范围-止
     */
    private String compareTimeEnd;
    /**
     * 审核时间-起
     */
    private String auditTimeBegin;
    /**
     * 审核时间-止
     */
    private String auditTimeEnd;
    /**
     * 相似度范围-起
     */
    private BigDecimal similarityBegin;
    /**
     * 相似度范围-止
     */
    private BigDecimal similarityEnd;
    /**
     * 审核状态
     */
    private Integer isTrumpet;
    /**
     * 审核人姓名
     */
    private String auditName;
    /**
     * 经纪人列表
     */
    private List<String> agentNameList;
    /**
     * 复核状态
     */
    private Integer stateFlag;
    /**
     * 部长是否已批注
     */
    private Integer isComment;
    /**
     * 公会id
     */
    private Integer guildId;

    private List<Integer> guildIds;
}
