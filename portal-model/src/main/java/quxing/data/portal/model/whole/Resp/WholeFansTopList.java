package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WholeFansTopList {

    private Long fansId;

    private Long tikTokId;

    private String tikTokNum;

    private String nickname;

    private String avatar;

    private String city;

    /**
     * 工会打赏金额
     */
    private Long guildRewardTotal;

    /**
     * 工会打赏次数
     */
    private Integer guildRewardNumber;

    private Long rewardTotal;

    private Integer rewardNumber;

    private Long guildAnchorNumber;

    private Long notGuildAnchorNumber;

    /**
     * 高打赏城市
     */
    private String ipNeeds;

    private String agentName;
}
