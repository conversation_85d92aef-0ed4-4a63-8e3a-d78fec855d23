package quxing.data.portal.model.live.result;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LiveLuckyBoxResult {
    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * 原抖音号
     */
    private Long tikTokId;
    /**
     * 现抖音号
     */
    private String tikTokNum;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 直播间地址
     */
    private String liveUrl;

    /**
     * 房间号
     */
    private Long roomId;
    /**
     * 当前直播观看人数
     */
    private Long userCount;
    /**
     * 直播观看总人次
     */
    private Long totalUser;

    /**
     * 领取时间
     */
    private Long receiveTime;
    /**
     * 红包类型
     */
    private Integer boxType;
    /**
     * 红包标题
     */
    private String title;
    /**
     * 金额
     */
    private Integer diamondCount;
}
