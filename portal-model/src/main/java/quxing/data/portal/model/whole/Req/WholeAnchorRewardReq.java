package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorRewardReq extends SortPageReq {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 开播日期
     */
    private String dateFlag;

    /**
     * 粉丝模糊查询条件
     */
    private String fansVague;
}
