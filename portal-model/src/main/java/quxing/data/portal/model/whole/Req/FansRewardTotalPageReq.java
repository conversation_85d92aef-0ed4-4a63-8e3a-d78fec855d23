package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

@EqualsAndHashCode(callSuper = true)
@Data
public class FansRewardTotalPageReq extends SortPageReq {
    private String beginDate;
    private String endDate;
    private Long beginDiamond;
    private Long endDiamond;
    private Boolean isRefresh;
}
