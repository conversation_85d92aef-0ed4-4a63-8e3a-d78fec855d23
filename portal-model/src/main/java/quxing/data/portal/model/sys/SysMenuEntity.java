package quxing.data.portal.model.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 系统菜单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-09 17:13:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_menu")
@Accessors(chain = true)
public class SysMenuEntity   {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 父菜单ID，一级菜单为0
     */
    private Integer parentId;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 前端菜单URL
     */
    private String url;
    /**
     * 授权
     */
    private String perms;

    /**
     * 类型
     *  1：菜单    2：按钮
     */
    private Integer type;

    /**
     * 方法 GET POST PUT DELETE PATCH
     */
    private String method;
    /**
     * 状态
     */
    private String status;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 是否显示
     */
    private Integer isShow;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 创建时间
     */
    private LocalDateTime createDatetime;
    /**
     * 修改时间
     */
    private LocalDateTime updateDatetime;
}
