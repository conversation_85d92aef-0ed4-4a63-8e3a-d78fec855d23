package quxing.data.portal.model.face.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class FaceCompareTimeoutResp {
    /**
     * 对比id
     */
    private Long compareId;
    /**
     * 公会主播id
     */
    private Long guildAnchorId;
    /**
     * 公会主播原抖音号
     */
    private String guildTikTokNum;
    /**
     * 公会主播现抖音号
     */
    private Long guildTikTokId;
    /**
     * 公会主播昵称
     */
    private String guildNickname;
    /**
     * 公会主播密文
     */
    private String guildSecUid;
    /**
     * 公会主播首页
     */
    private String guildHomepage;
    /**
     * 公会主播头像
     */
    private String guildAvatar;
    /**
     * 公会主播人脸图片地址
     */
    private String guildFaceUrl;
    //公会主播所属信息
    /**
     * 所属经纪人
     */
    private String guildAgentName;
    /**
     * 所属员工
     */
    private String guildEmployeeName;
    /**
     * 所属部门
     */
    private String guildDepartmentName;

    /**
     * 全网主播id
     */
    private Long wholeAnchorId;
    /**
     * 全网主播原抖音号
     */
    private String wholeTikTokNum;
    /**
     * 全网主播现抖音号
     */
    private Long wholeTikTokId;
    /**
     * 全网主播昵称
     */
    private String wholeNickname;
    /**
     * 全网主播密文
     */
    private String wholeSecUid;
    /**
     * 全网主播首页
     */
    private String wholeHomepage;
    /**
     * 全网主播头像
     */
    private String wholeAvatar;
    /**
     * 全网主播人脸id
     */
    private Integer wholeFaceId;
    /**
     * 全网主播人脸地址
     */
    private String wholeFaceUrl;
    /**
     * 相似度
     */
    private BigDecimal similarity;
    /**
     * 对比时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;
    /**
     * 处理时间
     */
    private String processingTime;

    /**
     * 是否正常处理
     */
    private Boolean isNormal;
}
