package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorRewardTotalParam extends SortPageReq {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 大哥密文
     */
    private String fansSecUid;

    /**
     * 开始日期
     */
    private String beginDate;

    /**
     * 结束日期
     */
    private String endDate;
}
