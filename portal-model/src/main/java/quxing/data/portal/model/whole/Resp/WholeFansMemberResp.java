package quxing.data.portal.model.whole.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeFansBaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 主播粉丝团列表
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeFansMemberResp extends WholeFansBaseEntity {

    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 亲密度等级
     */
    private Integer level;
    /**
     * 亲密度
     */
    private Long intimacy;
    /**
     * 打赏给主播的总音浪
     */
    private BigDecimal diamondCount;
    /**
     * 是否自动提醒
     */
    private Boolean autoLightUpStatus;
    /**
     * 入团时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private LocalDateTime participateTime;


}
