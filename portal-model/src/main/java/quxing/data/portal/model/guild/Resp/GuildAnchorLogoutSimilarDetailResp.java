package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GuildAnchorLogoutSimilarDetailResp {
    /**
     * 公会主播id
     */

    @JsonSerialize(using = ToStringSerializer.class)
    private Long guildAnchorId;
    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wholeAnchorId;

    /**
     * 原抖音号
     */
    private Long wholeTikTokId;

    /**
     * 现抖音号
     */
    private String wholeTikTokNum;

    /**
     * 主页地址
     */
    private String wholeHomepage;

    /**
     * 昵称
     */
    private String wholeNickname;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer wholeGender;

    /**
     * 签名
     */
    private String wholeSignature;

    /**
     * 头像
     */
    private String wholeAvatar;

    /**
     * 最后更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date wholeUpdateTime;

    /**
     * 主播人脸图片
     */
    private String wholeFaceUrl;

    /**
     * 工会主播人脸
     */
    private String guildFaceUrl;

    /**
     * 主播人脸截图时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wholeFaceTime;

    /**
     * 人脸截图时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wholeScreenshotTime;

    /**
     * 人脸对比表id
     */
    private Long compareId;

    /**
     * 人脸相似度
     */
    private String faceSimilarity;

    /**
     * 对比时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 公会id
     */
    private Integer oaGuildId;

    /**
     * 公会名
     */
    private String oaGuildName;

    /**
     * 是否已经进公会
     */
    private Boolean isGuild;

    /**
     * 进公会时间
     */
    private String joinDate;

    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;

    /**
     * 是否是小号 0-不确定 1-是小号 2-不是小号
     */
    private Integer isTrumpet;

    /**
     * 小号举报状态 0-未举报 1-已举报待处理 2-平台审核中 3-举报被驳回 4-举报已通过
     */
    private Integer reportStatus;

    /**
     * 操作人
     */
    private String createUserName;

    /**
     * 操作原因
     */
    private String trumpetReason;

    /**
     * 全网主播ip
     */
    private String wholeIpStr;
    /**
     * 疑似小号数
     */
    private Integer compareCount;
}
