package quxing.data.portal.model.face;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 人脸对比是否小号修改日志
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-27 11:37:58
 */
@Data
@TableName("face_trumpet_log")
public class FaceTrumpetLogEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@Excel(name = "审核id")
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 人脸对比id
	 */
	private Long faceCompareId;
	/**
	 * 是否是小号 0-不确定 1-是小号 2-不是小号
	 */
	@Excel(name = "是否是小号")
	private Integer isTrumpet;
	/**
	 * 小号判断原因
	 */
	@Excel(name = "小号判断原因")
	private String trumpetReason;
	/**
	 * 是否已批注
	 */
	private Integer isComment;
	/**
	 * 复核状态 0-未确认 1-确认无异常 2-有异常待回复 3-异常已处理
	 */
	private Integer stateFlag;
	/**
	 * 是否删除
	 */
	private Integer isDelete;
	/**
	 * 创建用户名
	 */
	@Excel(name = "审核人账号")
	private String createUsername;
	/**
	 * 创建时间
	 */
	@Excel(name = "审核时间")
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

}
