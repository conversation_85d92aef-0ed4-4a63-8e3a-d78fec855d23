package quxing.data.portal.model.guild.Req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GuildAnchorLogoutExportReq {

    /**
     * 指定导出的主播列表 如果未选择则默认导出所有主播
     */
    private List<Long> guildAnchorIds;

    /**
     * 所属部门
     */
    private Integer departmentId;

    /**
     * 员工模糊查询条件
     * 支持通过全id和姓名模糊查询
     */
    private String employeeVague;

    /**
     * 所属经纪人
     */
    private String agentName;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 断播天数
     */
    private Integer interruptDays;

    /**
     * 最后直播时间起
     */
    private String lastLiveTimeBegin;

    /**
     * 最后直播时间止
     */
    private String lastLiveTimeEnd;

    /**
     * 断播120天内
     */
    private String lastLiveTime120;

    /**
     * 最后直播前30日音浪起
     */
    private Long totalEarningBegin;

    /**
     * 最后直播前30日音浪止
     */
    private Long totalEarningEnd;

    /**
     * 人脸相似度范围-起
     */
    private BigDecimal similarityBegin;

    /**
     * 人脸相似度范围-止
     */
    private BigDecimal similarityEnd;
    /**
     * 是否已读
     */
    private Boolean isRead;
    /**
     * 是否已注销
     */
    private Boolean isLogout;
    /**
     * 是否已注销 数字
     */
    private Integer isLogoutNum;
    /**
     * 是否特别关心主播
     */
    private Boolean isCare;
    /**
     * 是否已上报到抖音 0-未上报 1-已上报 2-通过 3-拒绝 4-非小号
     */
    private Integer isReport;
    /**
     * 是否是小号 0-不确定 1-是小号 2-不是小号
     */
    private Integer isTrumpet;
    /**
     * 用户名
     */
    private String username;
    /**
     * 是否在本公会
     */
    private Boolean isGuild;
    /**
     * 用户所能管理的经纪人
     */
    private List<String> currentAgentList;
    /**
     * 断播天数范围起
     */
    private Integer interruptDaysBegin;
    /**
     * 断播天数范围止
     */
    private Integer interruptDaysEnd;
    /**
     * 公会id
     */
    private Integer guildId;
}
