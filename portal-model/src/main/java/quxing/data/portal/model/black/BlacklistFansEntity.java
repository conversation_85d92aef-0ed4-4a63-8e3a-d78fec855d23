package quxing.data.portal.model.black;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 黑名单粉丝表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-13 10:25:01
 */
@Data
@TableName("blacklist_fans")
public class BlacklistFansEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 粉丝id
	 */
	private Long fansId;
	/**
	 * 黑名单类型 0-其他 1-科技号
	 */
	private Integer typeFlag;
	/**
	 * 黑名单原因
	 */
	private String reason;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;

}
