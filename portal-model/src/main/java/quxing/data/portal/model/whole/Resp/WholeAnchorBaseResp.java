package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.anchor.AnchorBaseInfoEntity;
import quxing.data.portal.model.whole.WholeAnchorLiveEntity;
import quxing.data.portal.model.whole.WholeFansClubEntity;

/**
 * 主播详情返回值
 * <AUTHOR>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class WholeAnchorBaseResp extends AnchorBaseInfoEntity {
    /**
     * 主播开播情况
     */
    private WholeAnchorLiveEntity anchorLive;

    /**
     * 主播粉丝团情况
     */
    private WholeFansClubEntity fansClub;


}
