package quxing.data.portal.model.anchor.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 主播公开导入分页查询响应
 * <AUTHOR>
 */
@Data
public class AnchorPublicImportPageResp {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tiktokId;

    /**
     * 现抖音号
     */
    private String tiktokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 人脸地址
     */
    private String url;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
