package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class WholeLiveRewardListResp {

    /**
     * 大哥id
     */
    private Long fansId;

    /**
     * 打赏人加密id
     */
    private String fansSecUid;

    /**
     * 打赏数量
     */
    private Integer rewardNum;
    /**
     * 打赏物品总价 单位：音浪/抖币
     */
    private Long rewardTotal;

    /**
     * 工会打赏数量
     */
    private Integer guildRewardNum;
    /**
     * 工会打赏物品总价
     */
    private Long guildRewardTotal;


    //下面作用于大哥主播维度统计

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 打赏时间
     */
    private String createTime;
}
