package quxing.data.portal.model.face.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaceCheckResult {
    /**
     * 是否有人脸
     */
    private Boolean hasFace;
    /**
     * 是否是优质人脸
     */
    private Boolean isHighFace;
    /**
     * 提示语
     */
    private String message;
    /**
     * 唯一码
     */
    private String uuid;
}
