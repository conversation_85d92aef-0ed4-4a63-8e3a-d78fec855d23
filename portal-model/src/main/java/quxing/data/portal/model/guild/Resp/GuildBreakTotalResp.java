package quxing.data.portal.model.guild.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.guild.GuildBreakTotalEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildBreakTotalResp extends GuildBreakTotalEntity {
    private String faceUrl;

    private Integer checkFlag;

    private String checkMessage;

    private String secUid;

    private String avatar;

    private Long breakDays;

    private List<String> careFaceList;

    private Boolean existHighFace;

}
