package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 * @Date 2021/11/15 13:08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("video_face")
public class VideoFace {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long anchorId;

    private Long tikTokId;

    private String url;

    private Integer isCheck;

    private Integer checkFlag;

    private String checkMessage;

    private Integer age;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 是否参与比较
     */
    private Integer isCompare;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
