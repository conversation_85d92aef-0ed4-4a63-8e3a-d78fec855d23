package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * 获取指定主播的大哥统计传参
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeFansRewardTotalParam extends SortPageReq {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 大哥密文
     */
    private String fansSecUid;

    /**
     * 开始日期
     */
    private String beginDate;

    /**
     * 结束日期
     */
    private String endDate;
}
