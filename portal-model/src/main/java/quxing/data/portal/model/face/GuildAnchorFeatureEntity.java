package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公会主播人脸特征
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-01-10 13:36:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("guild_anchor_feature")
public class GuildAnchorFeatureEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 主播id
	 */
	private Long anchorId;
	/**
	 * 主播人脸特征
	 */
	private Object feature;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

}
