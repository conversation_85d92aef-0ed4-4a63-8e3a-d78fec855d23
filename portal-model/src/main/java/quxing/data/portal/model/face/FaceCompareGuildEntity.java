package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 公会主播人脸内部对比记录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-09 13:27:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("face_compare_guild")
public class FaceCompareGuildEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 主播id1
	 */
	private Long minAnchorId;
	/**
	 * 主播id1对应的原抖音号
	 */
	private Long minTikTokId;

	private String minFaceUrl;
	/**
	 * 主播id2
	 */
	private Long maxAnchorId;
	/**
	 * 主播id2对应的原抖音号
	 */
	private Long maxTikTokId;

	private String maxFaceUrl;
	/**
	 * 相似度
	 */
	private BigDecimal similarity;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
