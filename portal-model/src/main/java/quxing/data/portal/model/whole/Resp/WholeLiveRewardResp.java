package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeLiveRewardEntity;

/**
 * 打赏记录返回值
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeLiveRewardResp extends WholeLiveRewardEntity {
    /**
     * 主播昵称
     */
    private String anchorNickname;

    /**
     * 主播首页
     */
    private String anchorHomePage;

    /**
     * 直播间标题
     */
    private String anchorTitle;

    /**
     * 大哥id
     */
    private Long fansId;

    /**
     * 大哥原抖音号
     */
    private Long fansTikTokId;

    /**
     * 大哥现抖音号
     */
    private String fansTikTokNum;

    /**
     * 大哥昵称
     */
    private String fansNickname;

    /**
     * 打赏物品图标
     */
    private String iconUrl;
}
