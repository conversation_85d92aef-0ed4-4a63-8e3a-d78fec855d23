package quxing.data.portal.model.head;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 手动上传人脸对比记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 13:54:44
 */
@Data
@TableName("head_face_compare")
public class HeadFaceCompareEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private Long id;
	/**
	 * 手动上传人脸id
	 */
	private Long headFaceId;
	/**
	 * 工会主播id
	 */
	private Long guildAnchorId;
	/**
	 * 工会主播人脸图片地址
	 */
	private String guildAnchorFaceUrl;
	/**
	 * 相似度
	 */
	private BigDecimal similarity;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
