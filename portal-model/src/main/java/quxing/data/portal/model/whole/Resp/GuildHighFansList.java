package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GuildHighFansList {

    private Long fansId;

    private String nickname;
    /**
     * 原抖音号
     */
    private Long tikTokId;
    /**
     * 现抖音号
     */
    private String tikTokNum;

    private String ipStr;


    /**
     * 头像
     */
    private String avatar;

    private Integer gender;


    private String secUid;

    private Long rewardTotal;

    private Long rewardNum;

    private Long anchorCount;

    private Long guildRewardTotal;

    private Long guildRewardNum;

    private Long guildAnchorCount;

    private String liveTimes;

}
