package quxing.data.portal.model.face.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildCompareReq extends SortPageReq {
    private String minAnchorVague;

    private String maxAnchorVague;

    private String beginCompareTime;

    private String endCompareTime;

    private BigDecimal beginSimilarity;

    private BigDecimal endSimilarity;

    /**
     * 来源 0-公会人脸自比 1-全网人脸对比
     */
    private Integer sourceFlag;

    /**
     * 左侧公会
     */
    private Integer guildIdLeft;
    /**
     * 右侧公会
     */
    private Integer guildIdRight;
}
