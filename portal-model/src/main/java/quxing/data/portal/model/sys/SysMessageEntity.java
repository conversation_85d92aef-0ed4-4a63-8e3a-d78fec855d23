package quxing.data.portal.model.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 系统消息记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-02 16:03:46
 */
@Data
@TableName("sys_message")
public class SysMessageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 类型
	 */
	private String typeFlag;
	/**
	 * 标题
	 */
	private String title;
	/**
	 * 内容
	 */
	private String contentText;
	/**
	 * 路由地址
	 */
	private String urlAddress;
	/**
	 * 传参 json字符串
	 */
	private String conditionJson;
	/**
	 * 是否已读
	 */
	private Integer isRead;
	/**
	 * 是否删除
	 */
	private Integer isDelete;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="MM-dd HH:mm")
	private LocalDateTime createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="MM-dd HH:mm")
	private LocalDateTime updateTime;

}
