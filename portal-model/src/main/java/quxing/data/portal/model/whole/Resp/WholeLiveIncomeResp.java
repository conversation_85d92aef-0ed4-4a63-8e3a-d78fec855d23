package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeLiveRewardEntity;

/**
 * 收入记录返回值
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeLiveIncomeResp extends WholeLiveRewardEntity {

    /**
     * 打赏人原抖音号
     */
    private Long fansTikTokId;

    /**
     * 打赏人现抖音号
     */
    private String fansTikTokNum;

    /**
     * 打赏人昵称
     */
    private String fansNickname;

    /**
     * 打赏人首页地址
     */
    private String fansHomePage;
}
