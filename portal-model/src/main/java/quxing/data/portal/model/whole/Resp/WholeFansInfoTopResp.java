package quxing.data.portal.model.whole.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import quxing.data.portal.model.whole.WholeFansAnchorEntity;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class WholeFansInfoTopResp {

    private String secUid;

    private Long fansId;
    /**
     * 原抖音号
     */
    private Long tikTokId;
    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 城市
     */
    private String city;

    /**
     * 是否打赏过本公会
     */
    private Boolean isRewardGuild = false;

    /**
     * 累计打赏总金额
     */
    private Long allRewardNum = 0l;

    /**
     * 最高消费额主播详情
     */
    private WholeFansRewardInfo maxRewardInfo;

    /**
     * 最低消费额主播详情
     */
    private WholeFansRewardInfo minRewardInfo;

    /**
     * 最后打赏公会主播
     */
    private WholeFansRewardInfo lastRewardInfo;

    /**
     * 最后打赏时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastRewardTime;


    @Data
    public static class WholeFansRewardInfo {

        private Long anchorId;

        private Integer isGuild;

        private LocalDateTime updateTime;

        private Long rewardNum;

        private Long rewardTotal;

        private String nickname;

        private String ipNeed;
    }
}
