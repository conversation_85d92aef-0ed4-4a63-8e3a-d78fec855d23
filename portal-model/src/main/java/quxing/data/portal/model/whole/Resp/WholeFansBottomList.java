package quxing.data.portal.model.whole.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class WholeFansBottomList {

    /**
     * 主播id密文
     */
    private String secUid;

    private Long anchorId;
    /**
     * 原抖音号
     */
    private Long tikTokId;
    /**
     * 现抖音号
     */
    private String tikTokNum;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 头像地址
     */
    private String avatar;

    //ip归属
    private String ipNeed;

//    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    private LocalDateTime createTime;

    private String roomId;

    //打赏次数
    private Integer rewardNum;

    private Long rewardTotal;

    private Integer isGuild;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastRewardTime;
}
