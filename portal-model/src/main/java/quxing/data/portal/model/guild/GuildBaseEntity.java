package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@TableName("guild_base")
public class GuildBaseEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * oa主键
     */
    private Integer oaId;

    /**
     * 公会名称
     */
    private String name;

    /**
     * 公会所属标志 1-趣星 2-万达
     */
    private Integer belongFlag;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
