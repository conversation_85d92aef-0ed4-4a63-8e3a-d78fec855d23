package quxing.data.portal.model.whole.Resp;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.whole.WholeAnchorBaseEntity;
import quxing.data.portal.model.whole.WholeAnchorRewardEntity;

/**
 * 粉丝打赏情况返回值
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeFansRewardResp extends WholeAnchorRewardEntity {
    /**
     * 主播信息
     */
    private WholeAnchorBaseEntity anchorBase;
}
