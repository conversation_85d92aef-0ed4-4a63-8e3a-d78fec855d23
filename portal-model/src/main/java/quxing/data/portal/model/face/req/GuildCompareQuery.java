package quxing.data.portal.model.face.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildCompareQuery extends SortPageReq {
    /**
     * 部门id
     */
    private Integer departmentId;
    /**
     * 主播模糊条件
     */
    private String anchorKey;
    /**
     * 经纪人
     */
    private String agentName;
    /**
     * 经纪人集合
     */
    private List<String> agentNames;
    /**
     * 相似范围-起
     */
    private BigDecimal minSimilarity;
    /**
     * 相似范围-止
     */
    private BigDecimal maxSimilarity;
    /**
     * 所属机构
     */
    private Integer guildId;
    /**
     * 所属机构集合
     */
    private List<Integer> guildIds;
}
