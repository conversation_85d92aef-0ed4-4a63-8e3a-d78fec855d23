package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 查询人脸结果明细
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-02-10 11:49:56
 */
@Data
@TableName("query_face_detail")
public class QueryFaceDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 任务id
	 */
	private Long taskId;
	/**
	 * 主播id
	 */
	private Long anchorId;
	/**
	 * 主播人脸id
	 */
	private Integer faceId;
	/**
	 * 相似度
	 */
	private BigDecimal similarity;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
