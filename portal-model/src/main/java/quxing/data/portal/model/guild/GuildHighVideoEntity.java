package quxing.data.portal.model.guild;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 达人视频表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-30 16:54:22
 */
@Data
@TableName("guild_high_video")
public class GuildHighVideoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 主播号
	 */
	private Long anchorId;
	/**
	 * 房间号
	 */
	private Long roomId;
	/**
	 * 分片录制标识
	 */
	private Integer recordNum;
	/**
	 * 视频存放地址
	 */
	private String storageUrl;
	/**
	 * 视频时长 秒
	 */
	private Long duration;
	/**
	 * 是否删除
	 */
	private Integer isDelete;

	private LocalDateTime startTime;

	private LocalDateTime endTime;

	/**
	 * 创建时间
	 */
	@JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;
	/**
	 * 创建人
	 */
	private Integer createUser;

}
