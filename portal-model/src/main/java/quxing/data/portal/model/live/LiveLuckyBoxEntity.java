package quxing.data.portal.model.live;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 红包记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-30 09:19:23
 */
@Data
@TableName("live_lucky_box")
public class LiveLuckyBoxEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 主播号
	 */
	private Long anchorId;
	/**
	 * 房间号
	 */
	private Long roomId;
	/**
	 * 红包发送人id
	 */
	private Long boxAnchorId;
	/**
	 * 红包id
	 */
	private Long boxId;
	/**
	 * 发送时间
	 */
	private Long sendTime;
	/**
	 * 延时时长
	 */
	private Long delayTime;
	/**
	 * 领取时间
	 */
	private Long receiveTime;
	/**
	 * 红包类型
	 */
	private Integer boxType;
	/**
	 * 红包标题
	 */
	private String title;
	/**
	 * 金额
	 */
	private Integer diamondCount;
	/**
	 * 红包风格
	 */
	private Integer style;
	/**
	 * 是否是正式的
	 */
	private Integer isOfficial;
	/**
	 * 优先级
	 */
	private Integer priority;
	/**
	 * 是否是大红包
	 */
	private Integer large;
	/**
	 * 显示时长
	 */
	private Integer displayDuration;
	/**
	 * 红包状态
	 */
	private Integer boxStatus;
	/**
	 * 持续时间
	 */
	private Integer flatDuration;
	/**
	 * 打开类型
	 */
	private Integer unpackType;
	/**
	 * 领取条件
	 */
	private Integer rushCondition;
	/**
	 *
	 */
	private Integer businessType;
	/**
	 * 是否是随机红包
	 */
	private Integer isRiskyOwner;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
