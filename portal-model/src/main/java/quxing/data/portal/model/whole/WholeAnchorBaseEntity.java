package quxing.data.portal.model.whole;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 全网主播信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-08-08 17:43:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("whole_anchor_base")
public class WholeAnchorBaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主播id
     */
    @TableId(value = "anchor_id", type = IdType.INPUT)
    private Long anchorId;
    /**
     * 原抖音号
     */
    private Long tikTokId;
    /**
     * 现抖音号
     */
    private String tikTokNum;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;
    /**
     * 签名
     */
    private String signature;
    /**
     * 等级
     */
    private Integer level;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 头像地址
     */
    private String avatar;
    /**
     * 国籍
     */
    private String country;
    /**
     * 省份
     */
    private String province;
    /**
     * 位置
     */
    private String location;
    /**
     * 区
     */
    private String district;
    /**
     * 城市
     */
    private String city;
    /**
     * 消费等级
     */
    private Integer payGrade;
    /**
     * 主播id密文
     */
    private String secUid;
    /**
     * 粉丝数
     */
    private Long mplatformFollowersCount;
    /**
     * 获赞数
     */
    private Long totalFavorited;
    /**
     * 关注数
     */
    private Long followingCount;

    /**
     * 总音浪
     */
    private Long totalEarning;

    /**
     * 蓝V单位
     */
    private String enterpriseVerifyReason;
    /**
     * 是否通过蓝V认证
     */
    private Boolean isEnterpriseVerify;
    /**
     * 是否通过验证
     */
    private Boolean verified;
    /**
     * 是否已经过商业认证
     */
    private Boolean withCommercePermission;
    /**
     * 是否开启商店入口
     */
    private Boolean withFusionShopEntry;
    /**
     * 是否通过手机号验证
     */
    private Boolean verifiedMobile;
    /**
     * 该主播第一次直播时间 用于标识新主播
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date liveDateFlag;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * ip所属地
     */
    private String ipNeed;

    /**
     * 冗余字段(全网主播使用)
     */
    @TableField(exist = false)
    private String faceUrl;

    /**
     * 冗余字段(全网主播使用)
     */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime faceCreateTime;

    /**
     * 是否是目标主播 1-是 0-不是
     * 冗余字段
     */
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Integer isTarget;

    /**
     * 房间号
     * 冗余字段
     */
    @TableField(exist = false)
    private String roomId;

    /**
     * 当前是否开播
     * 冗余字段
     */
    @TableField(exist = false)
    private Integer isShow;
}