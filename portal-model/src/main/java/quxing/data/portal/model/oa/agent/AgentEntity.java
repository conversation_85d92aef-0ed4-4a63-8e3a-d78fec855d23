package quxing.data.portal.model.oa.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

/**
 * 运营号信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-10-14 13:10:35
 */
@Data
@TableName("agent")
public class AgentEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 所属部门
	 */
	private Integer depId;
	/**
	 * 所属员工
	 */
	private Integer empId;
	/**
	 * 管理人id
	 */
	private Integer custodianId;
	/**
	 * 经纪人名称
	 */
	private String agentName;
	/**
	 * 经纪人绑定员工状态
	 */
	private Integer state;
	/**
	 * 基础利润（分）
	 */
	private BigDecimal profitBase;
	/**
	 * 目标流水（分）
	 */
	private BigDecimal targetFlow;
	/**
	 * 基础总利润(员工)
	 */
	private BigDecimal profitBaseComEmp;
	/**
	 * 基础总利润(组长、部长)
	 */
	private BigDecimal profitBaseComUp;
	/**
	 * 提成比%
	 */
	private Integer commissionRatio;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	private LocalDateTime createTime;
	/**
	 * 创建人
	 */
	private Integer createUser;
	/**
	 * 修改时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	private LocalDateTime updateTime;
	/**
	 * 修改人
	 */
	private Integer updateUser;

}
