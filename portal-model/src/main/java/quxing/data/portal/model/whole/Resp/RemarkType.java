package quxing.data.portal.model.whole.Resp;


public enum RemarkType {

    HAVE_FACE(1, "已经有人脸信息"),
    CLOSE_LIVE(2, "直播已经关闭"),
    GAME_LIVE(3, "游戏直播"),
    INTERFACE_ERROR(4, "接口请求错误"),
    PK_LIVE(5, "直播间在PK"),
    STOP_LIVE(6, "直播已停播"),
    FFMPEG_ERROR(7, "直播流截图失败"),
    FACE_INTERFACE_ERROR(8, "人脸截图调用失败"),
    SUCCESS(200, "截图成功"),
    READ_IMG_ERROR(502, "读取图片路径失败"),
    NOT_FACE(503, "未识别到人脸"),
    UPLOAD_ERROR(504, "上传图片失败"),
    NGINX_REQ_ERROR(505, "图片地址请求失败"),
    EXCEPTION(400, "出现异常");

    private int key;
    private String remark;

    RemarkType(Integer value, String remark) {
        this.key = value;
        this.remark = remark;
    }

    public int getKey() {
        return this.key;
    }

    public String getValue() {
        return this.remark;
    }

    public static RemarkType getByCode(int code) {
        RemarkType[] values = RemarkType.values();
        for (RemarkType faceReqEnum : values) {
            if (faceReqEnum.getKey() == code) {
                return faceReqEnum;
            }
        }
        return null;
    }
}
