package quxing.data.portal.model.anchor.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * 主播公开导入分页查询请求
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnchorPublicImportPageReq extends SortPageReq {

    /**
     * 主播模糊查询条件
     * 支持：主播id、原抖音号、现抖音号、昵称
     */
    private String anchorVague;

    /**
     * 原抖音号
     */
    private Long tiktokId;

    /**
     * 现抖音号
     */
    private String tiktokNum;

    /**
     * 昵称
     */
    private String nickname;
}
