package quxing.data.portal.model.head;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 手动上传人脸表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 13:54:44
 */
@Data
@TableName("head_face")
public class HeadFaceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private Long id;
	/**
	 * 标题 用于检索
	 */
	private String title;
	/**
	 * 人脸地址
	 */
	private String url;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 对比类型 1-比公会 2-比全网
	 */
	private Integer typeFlag;
	/**
	 * 对比状态
	 */
	private Integer compareFlag;
	/**
	 * 对比情况返回值
	 */
	private String sysReturn;
	/**
	 * 创建用户名
	 */
	private String createUsername;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

}
