package quxing.data.portal.model.guild.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.PageReq;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GuildAnchorQuery extends PageReq {

    /**
     * 部门id
     */
    private Integer departmentId;

    private String agentName;

    /**
     * 经纪人
     */
    private List<String> agentNames;


    private String anchorKey;

    /**
     * 当前是否是高收入
     */
    private Boolean isNotHigh;


    private String startTime;


    private String endTime;


    private List<Long> anchorIds;


    private String sortField;

    private Boolean isAsc;

    private Boolean isLive;

    private Boolean isIngLive;
    /**
     * 公会id
     */
    private Integer guildId;

    private List<Integer> guildIds;

    /**
     * 是否特殊主播
     */
    private Boolean isSpecial;
}
