package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GuildAnchorFansSimilarDetailResp {
    /**
     * 公会主播id
     */
    private Long guildAnchorId;
    /**
     * 主播id
     */
    private Long wholeAnchorId;

    /**
     * 原抖音号
     */
    private Long wholeTikTokId;

    /**
     * 现抖音号
     */
    private String wholeTikTokNum;

    /**
     * 昵称
     */
    private String wholeNickname;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer wholeGender;

    /**
     * 签名
     */
    private String wholeSignature;

    /**
     * 头像
     */
    private String wholeAvatar;

    /**
     * 最后更新时间
     */
    @JsonFormat(timezone="GMT+8",pattern="yyyy-MM-dd HH:mm:ss")
    private Date wholeUpdateTime;

    /**
     * 粉丝团成员重复数
     */
    private Integer fansSimilarNum;

    /**
     * 主播人脸图片
     */
    private String wholeFaceUrl;
    /**
     * 相似度
     */
    private BigDecimal similarity;

    /**
     * 人脸相似度
     */
    private String faceSimilarity;

    /**
     * 公会主播id
     */
    private Integer oaGuildId;

    /**
     * 是否已经本公会
     */
    private Boolean isGuild;
}
