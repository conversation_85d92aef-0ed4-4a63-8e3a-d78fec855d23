package quxing.data.portal.model.guild.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class GuildTrumpetPageResp {
    //公会主播
    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long guildAnchorId;
    /**
     * 原抖音号
     */
    private Long guildTikTokId;
    /**
     * 现抖音号
     */
    private String guildTikTokNum;
    /**
     * 主播昵称
     */
    private String guildNickname;
    /**
     * 主播头像
     */
    private String guildAvatar;
    /**
     * 主播密文
     */
    private String guildSecUid;
    /**
     * 公会主播首页地址
     */
    private String guildHomePage;
    /**
     * 最后直播时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime guildLastLiveTime;
    /**
     * 断播天数
     */
    private Long interruptDays;

    /**
     * 所属部门id
     */
    private Integer guildDepartmentId;
    /**
     * 所属部门名称
     */
    private String guildDepartmentName;
    /**
     * 所属员工姓名
     */
    private String guildEmployeeName;
    /**
     * 所属经纪人
     */
    private String guildAgentName;

    /**
     * 公会主播人脸
     */
    private String guildFaceUrl;
    /**
     * 总音浪
     */
    private Long guildTotalEarning;

    /**
     * 所属公会
     */
    private Integer guildId;

    //全网主播
    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wholeAnchorId;
    /**
     * 原抖音号
     */
    private Long wholeTikTokId;
    /**
     * 现抖音号
     */
    private String wholeTikTokNum;
    /**
     * 主播昵称
     */
    private String wholeNickname;
    /**
     * 主播头像
     */
    private String wholeAvatar;
    /**
     * 主播密文
     */
    private String wholeSecUid;
    /**
     * 首页地址
     */
    private String wholeHomepage;

    /**
     * 人脸id
     */
    private Integer wholeFaceId;

    /**
     * 主播人脸
     */
    private String wholeFaceUrl;
    /**
     * 人脸截图时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime wholeFaceTime;

    /**
     * 人脸相似度
     */
    private BigDecimal similarity;
    /**
     * 对比时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime compareTime;

    /**
     * 疑似小号数
     */
    private Integer trumpetNum;

    /**
     * 对比主键
     */
    private Long compareId;

    /**
     * 平均3个月音浪
     */
    private Long avg3MonthSound;
}
