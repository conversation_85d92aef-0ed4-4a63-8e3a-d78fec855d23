package quxing.data.portal.model.link.result;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class LiveLinkDetailPageResult {
    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 首页地址
     */
    private String homePage;

    /**
     * 粉丝数
     */
    private Long mplatformFollowersCount;

    /**
     * 最近开播位置
     */
    private String location;

    /**
     * 连线时间
     */
    private LocalDateTime createTime;
}
