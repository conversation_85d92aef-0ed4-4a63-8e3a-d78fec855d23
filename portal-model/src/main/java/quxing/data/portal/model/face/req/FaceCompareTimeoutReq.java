package quxing.data.portal.model.face.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.PageReq;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceCompareTimeoutReq extends PageReq {
    /**
     * 所属部门
     */
    private Integer departmentId;
    /**
     * 日期
     */
    private String dateFlag;

    private LocalDateTime beginTime;
    private LocalDateTime endTime;
    private LocalDateTime lastLiveTime;
    private LocalDateTime cutoffTime;

    private String agentName;

    private List<String> agentNameList;
    /**
     * 公会id
     */
    private Integer guildId;
}
