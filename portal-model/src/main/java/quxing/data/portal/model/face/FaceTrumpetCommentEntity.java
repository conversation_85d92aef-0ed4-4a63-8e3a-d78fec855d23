package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 人脸小号审核记录批注表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-03-29 11:26:03
 */
@Data
@TableName("face_trumpet_comment")
public class FaceTrumpetCommentEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	/**
	 * 记录id
	 */
	private Long logId;
	/**
	 * 是否是小号 1-是小号 2-不是小号
	 */
	private Integer isTrumpet;
	/**
	 * 小号判断原因
	 */
	private String trumpetReason;
	/**
	 * 创建用户名
	 */
	private String createUsername;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	private LocalDateTime createTime;
}
