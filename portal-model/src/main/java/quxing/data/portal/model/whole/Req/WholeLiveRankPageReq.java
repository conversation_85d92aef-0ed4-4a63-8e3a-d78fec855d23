package quxing.data.portal.model.whole.Req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import quxing.data.portal.model.common.Req.SortPageReq;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WholeLiveRankPageReq extends SortPageReq {
    /**
     * 榜单类型
     */
    private Integer typeFlag;
    /**
     * 主播模糊查询条件
     */
    private String anchorVague;
    /**
     * 上榜时间范围-起
     */
    private String beginTime;
    /**
     * 上榜时间范围-止
     */
    private String endTime;
    /**
     * 性别
     */
    private Integer gender;
    /**
     * 年龄范围起
     */
    private Integer beginAge;
    /**
     * 年龄范围止
     */
    private Integer endAge;
}
