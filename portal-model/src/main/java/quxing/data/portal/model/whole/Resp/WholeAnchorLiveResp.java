package quxing.data.portal.model.whole.Resp;

import lombok.*;
import quxing.data.portal.model.anchor.AnchorBaseInfoEntity;
import quxing.data.portal.model.whole.WholeAnchorBaseEntity;
import quxing.data.portal.model.whole.WholeAnchorLiveEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 主播开播列表返回值
 * <AUTHOR>
 */
@Data
public class WholeAnchorLiveResp {
    /**
     * 开播日期
     */
    private LocalDate dateFlag;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播首页
     */
    private String anchorHomePage;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 开播时间
     */
    private LocalDateTime liveCreateTime;

    /**
     * 直播结束时间
     */
    private LocalDateTime liveFinishTime;

    /**
     * 是否本公会主播
     */
    private Boolean isSelfGuild;

    /**
     * 主播信息
     */
    private WholeAnchorBaseEntity wholeAnchorBase;

    /**
     * 开播信息
     */
    private WholeAnchorLiveEntity wholeAnchorLive;

    /**
     * 公会信息
     */
    private AnchorBaseInfoEntity anchorBaseInfo;

}
