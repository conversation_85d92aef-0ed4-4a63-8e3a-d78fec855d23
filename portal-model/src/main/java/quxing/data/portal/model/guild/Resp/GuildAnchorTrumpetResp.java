package quxing.data.portal.model.guild.Resp;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GuildAnchorTrumpetResp {
    /**
     * 公会主播id
     */
    private Long guildAnchorId;
    /**
     * 公会主播人脸
     */
    private String guildFaceUrl;
    /**
     * 小号主播id
     */
    private Long trumpetAnchorId;
    /**
     * 小号原抖音号
     */
    private Long trumpetTikTokId;
    /**
     * 小号现抖音号
     */
    private String trumpetTikTokNum;
    /**
     * 小号昵称
     */
    private String trumpetNickname;
    /**
     * 小号公会id
     */
    private Integer trumpetGuildId;
    /**
     * 小号公会名称
     */
    private String trumpetGuildName;
    /**
     * 小号头像地址
     */
    private String trumpetAvatar;
    /**
     * 小号运营号名称
     */
    private String trumpetAgentName;
    /**
     * 小号30日收入（音浪）
     */
    private Long trumpetTotalEarning;
    /**
     * 小号密文
     */
    private String trumpetSecUid;
    /**
     * 小号首页地址
     */
    private String trumpetHomePage;
    /**
     * 小号人脸
     */
    private String trumpetFaceUrl;
    /**
     * 备注
     */
    private String remarkContent;
    /**
     * 是否是小号
     */
    private Integer isTrumpet;
}
