package quxing.data.portal.model.face;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 全网主播人脸图片表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-12-17 10:40:15
 */
@Data
@TableName("whole_anchor_face")
public class WholeAnchorFaceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	/**
	 * 主播id
	 */
	private Long anchorId;
	/**
	 * 地址
	 */
	private String url;
	/**
	 * 性别
	 */
	private Integer gender;

	/**
	 * 是否已提取人脸特征
	 */
	private Integer isGet;
	/**
	 * 是否有效
	 */
	private Integer isValid;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 修改时间
	 */
	private LocalDateTime updateTime;

}
