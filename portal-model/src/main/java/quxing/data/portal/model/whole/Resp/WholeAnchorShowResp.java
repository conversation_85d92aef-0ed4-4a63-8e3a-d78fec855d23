package quxing.data.portal.model.whole.Resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class WholeAnchorShowResp {
    /**
     * 主播id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long anchorId;

    /**
     * 原抖音号
     */
    private Long tikTokId;

    /**
     * 现抖音号
     */
    private String tikTokNum;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别 0-未知 1-男 2-女
     */
    private Integer gender;

    /**
     * 签名
     */
    private String signature;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 首页地址
     */
    private String homePage;

    /**
     * 开播日期
     */
    private LocalDate dateFlag;

    /**
     * 直播间号
     */
    private String roomId;

    /**
     * 标题
     */
    private String title;

    /**
     * 直播观看人次
     */
    private Long userCount;

    /**
     * 直播观看总人次
     */
    private Long totalUser;

    /**
     * 直播(音浪)
     */
    private Long fansTicket;

    /**
     * 新增粉丝数
     */
    private Long followCount;

    /**
     * 直播创建时间
     */
    private Integer liveCreateTime;

    /**
     * 关播时间
     */
    private Integer liveFinishTime;

    /**
     * 视频源地址
     */
    private String rtmpPullUrl;

    /**
     * 是否已有人脸
     */
    private Integer isFace;

    private String faceUrl;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime faceCreateTime;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
