package quxing.data.portal.controller.anchor;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import quxing.data.portal.model.anchor.Req.AnchorPublicImportPageReq;
import quxing.data.portal.model.common.Resp.R;
import quxing.data.portal.service.anchor.AnchorPublicImportService;

/**
 * 主播公开导入管理
 * <AUTHOR>
 */
@RestController
@RequestMapping("/anchor/public/import")
@RequiredArgsConstructor
public class AnchorPublicImportController {

    private final AnchorPublicImportService anchorPublicImportService;

    /**
     * 导入Excel数据
     */
    @PostMapping
    public R importData(@RequestParam("file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return R.error("请选择要导入的Excel文件");
        }

        String result = anchorPublicImportService.importData(file);

        if (result.contains("成功")) {
            return R.ok(result);
        } else {
            return R.error(result);
        }
    }

    /**
     * 分页查询列表
     */
    @GetMapping
    public R getPageList(AnchorPublicImportPageReq req) {
        return R.ok("", anchorPublicImportService.getPageList(req));
    }
}
