package quxing.data.portal.service.sys.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import quxing.data.portal.mapper.sys.SysUserGuildDao;
import quxing.data.portal.model.sys.SysUserGuildEntity;
import quxing.data.portal.service.sys.SysUserGuildService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 */
@Service("sysUserGuildService")
@RequiredArgsConstructor
public class SysUserGuildServiceImpl extends ServiceImpl<SysUserGuildDao, SysUserGuildEntity> implements SysUserGuildService {
    private static Map<String, List<Integer>> guildIdsForUserSta = new HashMap<>();
    private static ReentrantLock refreshLock = new ReentrantLock();
    private static LocalDateTime refreshTimeSta = LocalDateTime.now().minusHours(6);

    @Override
    public void refresh() {
        if (!refreshLock.tryLock()) {
            return;
        }
        try {
            List<SysUserGuildEntity> sysUserGuildList = this.list();
            if (CollUtil.isEmpty(sysUserGuildList)) {
                return;
            }
            Map<String, List<Integer>> guildIdsForUser = new HashMap<>();
            for (SysUserGuildEntity sysUserGuildI : sysUserGuildList) {
                List<Integer> guildIdsI = strToList(sysUserGuildI.getGuildIds());
                if (CollUtil.isEmpty(guildIdsI)) {
                    continue;
                }
                guildIdsForUser.put(sysUserGuildI.getUsername(), guildIdsI);
            }
            guildIdsForUserSta = guildIdsForUser;
            refreshTimeSta = LocalDateTime.now();
        }
        finally {
            refreshLock.unlock();
        }
    }

    public static List<Integer> strToList(String str) {
        if (StrUtil.isBlank(str)) {
            return null;
        }
        List<Integer> integerList = new ArrayList<>();
        String[] strArray = str.split(",");
        for (String strI : strArray) {
            if (StrUtil.isBlank(strI)) {
                continue;
            }
            integerList.add(Integer.parseInt(strI.trim()));
        }
        return integerList;
    }

    public static String listToStr(List<Integer> list) {
        if (CollUtil.isEmpty(list)) {
            return "";
        }
        StringBuilder str = new StringBuilder(list.get(0).toString());
        for (int i = 1; i < list.size(); i++) {
            Integer intI = list.get(i);
            if (intI == null) {
                continue;
            }
            str.append(",").append(intI);
        }
        return str.toString();
    }

    @Override
    public List<Integer> getGuildIds(String username) {
        if (refreshTimeSta.compareTo(LocalDateTime.now().minusHours(1)) < 0) {
            this.refresh();
        }
        return guildIdsForUserSta.get(username);
    }

    @Override
    public boolean save(String username, List<Integer> guildIds) {
        SysUserGuildEntity sysUserGuild = this.getOne(
                new QueryWrapper<SysUserGuildEntity>().eq("username", username)
        );
        if (sysUserGuild == null) {
            return this.save(
                    SysUserGuildEntity.builder()
                            .username(username)
                            .guildIds(listToStr(guildIds))
                            .createTime(LocalDateTime.now())
                            .updateTime(LocalDateTime.now())
                            .build()
            );
        }
        else {
            return this.updateById(
                    SysUserGuildEntity.builder()
                            .id(sysUserGuild.getId())
                            .guildIds(listToStr(guildIds))
                            .updateTime(LocalDateTime.now())
                            .build()
            );
        }
    }
}
