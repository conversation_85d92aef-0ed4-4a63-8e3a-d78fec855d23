package quxing.data.portal.service.anchor;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import quxing.data.portal.model.anchor.AnchorPublicImportEntity;
import quxing.data.portal.model.anchor.Req.AnchorPublicImportPageReq;
import quxing.data.portal.utils.PageUtil;

/**
 * <AUTHOR>
 */
public interface AnchorPublicImportService extends IService<AnchorPublicImportEntity> {

    /**
     * 导入Excel数据
     * @param file Excel文件
     * @return 导入结果信息
     */
    String importData(MultipartFile file);

    /**
     * 分页查询列表
     * @param req 查询条件
     * @return 分页数据
     */
    PageUtil getPageList(AnchorPublicImportPageReq req);
}
