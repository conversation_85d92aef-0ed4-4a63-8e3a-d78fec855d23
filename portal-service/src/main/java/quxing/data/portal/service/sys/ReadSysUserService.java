package quxing.data.portal.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import quxing.data.portal.model.sys.Req.SysUserPageReq;
import quxing.data.portal.model.sys.Req.SysUserResetReq;
import quxing.data.portal.model.sys.Req.SysUserSaveReq;
import quxing.data.portal.model.sys.Req.SysUserSetPwdReq;
import quxing.data.portal.model.sys.Resp.SysUserResp;
import quxing.data.portal.model.sys.Resp.SysUserRoleResp;
import quxing.data.portal.model.sys.SysUserEntity;
import quxing.data.portal.utils.PageUtil;

/**
 * 系统用户表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-07-09 17:13:00
 */
public interface ReadSysUserService extends IService<SysUserEntity> {
    /**
     * 基础增
     * @return 新增结果
     */
    boolean add(SysUserSaveReq sysUserSaveReq);

    /**
     * 根据主键修改
     * @return 修改结果
     */
    boolean updateByKey(SysUserSaveReq sysUserSaveReq);

    /**
     * 根据用户id重置密码
     * @param sysUserResetReq 重置密码传参
     * @return 重置结果
     */
    boolean resetPassWord(SysUserResetReq sysUserResetReq);

    /**
     * 修改当前用户的密码
     */
    boolean alterPassword(String oldPassword, String newPassword);

    /**
     * 获取用户的信息
     * @param username 用户名
     * @return 用户信息
     */
    SysUserEntity getUser(String username);

    /**
     * 根据主键删除用户
     * @param id 主键
     * @return 删除结果
     */
    boolean deleteById(Long id);

    /**
     * 用户分页查
     * @param sysUserPageReq 分页查询条件
     * @return 分页查询结果
     */
    PageUtil getPage(SysUserPageReq sysUserPageReq);

    /**
     * 获取用户信息
     * @return 用户信息
     */
    SysUserResp getUserByCurrent();

    /**
     * 获取用户基础信息
     */
    SysUserEntity getCurrentUser();

    /**
     * 获取用户与角色信息
     */
    SysUserRoleResp getCurrentUserRole();

    /**
     * 当前用户首次登录，设置密码
     */
    boolean setPassWord(SysUserSetPwdReq sysUserSetPwdReq);

    /**
     * 判断当前登录用户，是否首次登录
     */
    boolean isFirst();
}

