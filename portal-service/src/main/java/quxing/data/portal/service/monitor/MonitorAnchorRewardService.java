package quxing.data.portal.service.monitor;

import com.baomidou.mybatisplus.extension.service.IService;
import quxing.data.portal.model.monitor.MonitorAnchorRewardEntity;
import quxing.data.portal.utils.PageUtil;

/**
 * 监控指定主播被打赏情况
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-01-27 15:13:57
 */
public interface MonitorAnchorRewardService extends IService<MonitorAnchorRewardEntity> {

    boolean saveOne(MonitorAnchorRewardEntity monitorAnchorReward);

    boolean logicDeleteById(Long id);

    PageUtil getPage(String anchorVague, Long page, Long size, String sortField, Boolean isAsc);

    void refreshRewardCount();
}

