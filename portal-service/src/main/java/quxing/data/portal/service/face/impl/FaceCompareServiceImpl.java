package quxing.data.portal.service.face.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import quxing.data.portal.mapper.oa.OaCommonDao;
import quxing.data.portal.model.face.req.FaceCompareTimeoutReq;
import quxing.data.portal.model.face.req.FaceCompareTrumpetReq;
import quxing.data.portal.model.face.resp.FaceCompareRecordTotalResp;
import quxing.data.portal.model.face.resp.FaceCompareTimeoutResp;
import quxing.data.portal.model.oa.DeductionParam;
import quxing.data.portal.model.oa.agent.AgentResult;
import quxing.data.portal.model.oa.department.bean.DepartmentAndEmployeeBean;
import quxing.data.portal.model.sys.Resp.SysUserResp;
import quxing.data.portal.model.trumpet.TrumpetTreatmentStatisticEntity;
import quxing.data.portal.service.api.OaApiService;
import quxing.data.portal.service.face.FaceCompareRecordService;
import quxing.data.portal.service.face.FaceCompareService;
import quxing.data.portal.service.sys.SysRoleService;
import quxing.data.portal.service.sys.SysUserService;
import quxing.data.portal.service.trumpet.TrumpetTreatmentStatisticService;
import quxing.data.portal.utils.PageUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service("faceCompareService")
public class FaceCompareServiceImpl implements FaceCompareService {
    private final OaApiService oaApiService;
    private final FaceCompareRecordService faceCompareRecordService;
    private final SysRoleService sysRoleService;
    private final SysUserService sysUserService;
    private final TrumpetTreatmentStatisticService trumpetTreatmentStatisticService;

    private final OaCommonDao oaCommonDao;

    @Qualifier("scheduledAsyncExecutor")
    private final ThreadPoolTaskExecutor scheduledAsyncExecutor;

    @Override
    public PageUtil getTimeoutTrumpet(FaceCompareTimeoutReq req) {
        //根据OA的经纪人返回值进行数据隔离
        Boolean isSupper = sysRoleService.isSupperByCurrent();
        if (isSupper == null) {
            return new PageUtil(req.getSize(), req.getPage());
        }
        SysUserResp userByCurrent = sysUserService.getUserByCurrent();
        if (userByCurrent == null) {
            return new PageUtil(req.getSize(), req.getPage());
        }
        List<AgentResult> agentByUser = oaApiService.getAgentByUserByCache(
                req.getDepartmentId(),
                null,
                StrUtil.removePrefix(userByCurrent.getUsername(), "oa|"),
                Collections.singletonList(req.getGuildId())
        );
        if (!isSupper || req.getDepartmentId() != null || StrUtil.isEmpty(req.getAgentName())) {
            if (CollUtil.isEmpty(agentByUser)) {
                return new PageUtil(req.getSize(), req.getPage());
            }
        }
        Map<String, AgentResult> agentForName = agentByUser.stream()
                .collect(Collectors.toMap(AgentResult::getAgentName, t -> t));

        req.setAgentNameList(
                agentByUser.stream()
                        .map(AgentResult::getAgentName)
                        .collect(Collectors.toList())
        );

        LocalDate dateFlag;
        if (StrUtil.isNotEmpty(req.getDateFlag())) {
            dateFlag = LocalDate.parse(req.getDateFlag());
        }
        else {
            dateFlag = LocalDate.now();
        }
        req.setBeginTime(LocalDateTime.of(dateFlag.minusDays(1), LocalTime.of(20, 30, 1)));
        req.setEndTime(LocalDateTime.of(dateFlag, LocalTime.of(20, 30, 0)));
        req.setLastLiveTime(LocalDateTime.of(dateFlag.minusDays(120), LocalTime.MIN));
        req.setCutoffTime(LocalDateTime.of(dateFlag.plusDays(1), LocalTime.of(1, 0, 0)));

        PageInfo<FaceCompareTimeoutResp> pageInfo = faceCompareRecordService.getTimeoutTrumpet(req);
        if (pageInfo.getList() == null) {
            return new PageUtil(pageInfo);
        }
        for (FaceCompareTimeoutResp faceCompareTimeoutI : pageInfo.getList()) {
            if (StrUtil.isEmpty(faceCompareTimeoutI.getGuildAgentName())) {
                continue;
            }
            AgentResult agentResultI = agentForName.get(faceCompareTimeoutI.getGuildAgentName());
            if (agentResultI == null) {
                continue;
            }
            faceCompareTimeoutI.setGuildDepartmentName(agentResultI.getDepName());
            faceCompareTimeoutI.setGuildEmployeeName(agentResultI.getEmpName());
        }
        return new PageUtil(pageInfo);
    }

    @Override
    public PageUtil getTrumpet(FaceCompareTrumpetReq req) {
        //根据OA的经纪人返回值进行数据隔离
        Boolean isSupper = sysRoleService.isSupperByCurrent();
        if (isSupper == null) {
            return new PageUtil(req.getSize(), req.getPage());
        }
        SysUserResp userByCurrent = sysUserService.getUserByCurrent();
        if (userByCurrent == null) {
            return new PageUtil(req.getSize(), req.getPage());
        }
        List<AgentResult> agentByUser = oaApiService.getAgentByUserByCache(
                req.getDepartmentId(),
                null,
                StrUtil.removePrefix(userByCurrent.getUsername(), "oa|"),
                req.getGuildIds()
        );
        if (!isSupper || req.getDepartmentId() != null || StrUtil.isEmpty(req.getAgentName())) {
            if (CollUtil.isEmpty(agentByUser)) {
                return new PageUtil(req.getSize(), req.getPage());
            }
        }
        Map<String, List<AgentResult>> agentForName = agentByUser.stream()
                .collect(Collectors.groupingBy(AgentResult::getAgentName));

        req.setAgentNameList(
                agentByUser.stream()
                        .map(AgentResult::getAgentName)
                        .collect(Collectors.toList())
        );

        LocalDate dateFlag;
        if (StrUtil.isNotEmpty(req.getDateFlag())) {
            dateFlag = LocalDate.parse(req.getDateFlag());
        }
        else {
            dateFlag = LocalDate.now();
        }
        req.setBeginTime(LocalDateTime.of(dateFlag.minusDays(1), LocalTime.of(20, 0, 1)));
        req.setEndTime(LocalDateTime.of(dateFlag, LocalTime.of(20, 0, 0)));
        req.setLastLiveTime(LocalDateTime.of(dateFlag.minusDays(120), LocalTime.MIN));
        req.setCutoffTime(LocalDateTime.of(dateFlag.plusDays(1), LocalTime.of(1, 0, 0)));

        PageInfo<FaceCompareTimeoutResp> pageInfo = faceCompareRecordService.getTrumpet(req);
        if (pageInfo.getList() == null) {
            return new PageUtil(pageInfo);
        }
        for (FaceCompareTimeoutResp faceCompareTimeoutI : pageInfo.getList()) {
            if (StrUtil.isEmpty(faceCompareTimeoutI.getGuildAgentName())) {
                continue;
            }
            List<AgentResult> agentResultsI = agentForName.get(faceCompareTimeoutI.getGuildAgentName());
            if (CollUtil.isEmpty(agentResultsI)) {
                continue;
            }
            AgentResult agentResultI = agentResultsI.get(0);
            if (agentResultI == null) {
                continue;
            }
            faceCompareTimeoutI.setGuildDepartmentName(agentResultI.getDepName());
            faceCompareTimeoutI.setGuildEmployeeName(agentResultI.getEmpName());
        }
        return new PageUtil(pageInfo);
    }

    @Override
    public void trumpetDeductionAsync(LocalDate dateFlag) {
        List<DepartmentAndEmployeeBean> departmentIdAndEmpIdBeans = oaCommonDao.selectDepartmentIdByParentId(376);
        if (CollUtil.isEmpty(departmentIdAndEmpIdBeans)) {
            return;
        }
        LocalDateTime startDateTime = LocalDateTime.of(dateFlag.minusDays(3), LocalTime.of(20, 0, 1));
        LocalDateTime endDateTime = LocalDateTime.of(dateFlag.minusDays(1), LocalTime.of(20, 0, 0));
        List<DeductionParam> deductionParamList = new ArrayList<>();
        List<TrumpetTreatmentStatisticEntity> trumpetTreatmentStatisticList = new LinkedList<>();
        departmentIdAndEmpIdBeans.forEach(departmentIdAndEmpIdI -> {
            if (departmentIdAndEmpIdI == null) {
                return;
            }

            List<AgentResult> agentByUserI = oaApiService.getAgentByUserByCache(
                    departmentIdAndEmpIdI.getDepartmentId(),
                    "",
                    departmentIdAndEmpIdI.getUsername(),
                    null
            );
            if (CollUtil.isEmpty(agentByUserI)) {
                trumpetTreatmentStatisticList.add(
                        TrumpetTreatmentStatisticEntity.builder()
                                .dateFlag(dateFlag)
                                .departmentId(departmentIdAndEmpIdI.getDepartmentId())
                                .departmentName(departmentIdAndEmpIdI.getDepartmentName())
                                .totalNum(0)
                                .normalNum(0)
                                .abnormalNum(0)
                                .treatmentRate(100)
                                .createTime(LocalDateTime.now())
                                .build()
                );
                return;
            }
            List<String> agentNameListI = agentByUserI.stream().map(AgentResult::getAgentName).collect(Collectors.toList());
            FaceCompareRecordTotalResp faceCompareRecordTotalRespI = faceCompareRecordService.getAuditRatioByAgent(null, agentNameListI, startDateTime, endDateTime);
            trumpetTreatmentStatisticList.add(
                    TrumpetTreatmentStatisticEntity.builder()
                            .dateFlag(dateFlag)
                            .departmentId(departmentIdAndEmpIdI.getDepartmentId())
                            .departmentName(departmentIdAndEmpIdI.getDepartmentName())
                            .totalNum(faceCompareRecordTotalRespI.getTotalNum())
                            .normalNum(faceCompareRecordTotalRespI.getAuditNum())
                            .abnormalNum(faceCompareRecordTotalRespI.getTotalNum() - faceCompareRecordTotalRespI.getAuditNum())
                            .treatmentRate(faceCompareRecordTotalRespI.getAuditRatio().intValue())
                            .createTime(LocalDateTime.now())
                            .build()
            );

            if (faceCompareRecordTotalRespI.getTotalNum() == 0
                    || faceCompareRecordTotalRespI.getAuditRatio().compareTo(BigDecimal.ZERO) > 0
            ) {
                //审核数大于0则不罚款
                return;
            }
            DeductionParam deductionParamI = new DeductionParam();
            deductionParamI.setDateFlag(endDateTime.toLocalDate().toString());
            deductionParamI.setAmount(BigDecimal.valueOf(50));
            deductionParamI.setEmpId(departmentIdAndEmpIdI.getEmployeeId());
            String reasonI = "两天的小号的审核率未超过0%(截止到" + LocalDate.now() + "凌晨1点)" + "：需处理" + faceCompareRecordTotalRespI.getTotalNum() + "，实际处理" + faceCompareRecordTotalRespI.getAuditNum() + "，处理率" + faceCompareRecordTotalRespI.getAuditRatio() + "%";
            deductionParamI.setReason(reasonI);
            deductionParamList.add(deductionParamI);
        });
        if (CollUtil.isNotEmpty(trumpetTreatmentStatisticList)) {
            trumpetTreatmentStatisticService.saveBatch(trumpetTreatmentStatisticList);
        }

        if (CollUtil.isNotEmpty(deductionParamList)) {
            oaApiService.setDeduction(deductionParamList);
        }
    }

    @Override
    public void trumpetDeductionNotAuditedAsync(LocalDate dateFlag) {
        scheduledAsyncExecutor.execute(() -> trumpetDeductionNotAudited(dateFlag));
    }

    private void trumpetDeductionNotAudited(LocalDate dateFlag) {
        List<DepartmentAndEmployeeBean> departmentIdAndEmpIdBeans = oaCommonDao.selectDepartmentIdByParentId(133);
        if (CollUtil.isEmpty(departmentIdAndEmpIdBeans)) {
            return;
        }
        LocalDateTime startDateTime = LocalDateTime.of(dateFlag.minusDays(1), LocalTime.of(20, 0, 1));
        LocalDateTime endDateTime = LocalDateTime.of(dateFlag, LocalTime.of(20, 0, 0));
        List<DeductionParam> deductionParamList = new ArrayList<>();
        List<TrumpetTreatmentStatisticEntity> trumpetTreatmentStatisticList = new LinkedList<>();
        departmentIdAndEmpIdBeans.forEach(departmentIdAndEmpIdI -> {
            if (departmentIdAndEmpIdI == null) {
                return;
            }

            List<AgentResult> agentByUserI = oaApiService.getAgentByUserByCache(
                    departmentIdAndEmpIdI.getDepartmentId(),
                    "",
                    departmentIdAndEmpIdI.getUsername(),
                    null
            );
            if (CollUtil.isEmpty(agentByUserI)) {
                trumpetTreatmentStatisticList.add(
                        TrumpetTreatmentStatisticEntity.builder()
                                .dateFlag(dateFlag)
                                .departmentId(departmentIdAndEmpIdI.getDepartmentId())
                                .departmentName(departmentIdAndEmpIdI.getDepartmentName())
                                .totalNum(0)
                                .normalNum(0)
                                .abnormalNum(0)
                                .treatmentRate(100)
                                .createTime(LocalDateTime.now())
                                .build()
                );
                return;
            }
            List<String> agentNameListI = agentByUserI.stream().map(AgentResult::getAgentName).collect(Collectors.toList());
            FaceCompareRecordTotalResp faceCompareRecordTotalRespI = faceCompareRecordService.getAuditRatioByAgent(null, agentNameListI, startDateTime, endDateTime);
            trumpetTreatmentStatisticList.add(
                    TrumpetTreatmentStatisticEntity.builder()
                            .dateFlag(dateFlag)
                            .departmentId(departmentIdAndEmpIdI.getDepartmentId())
                            .departmentName(departmentIdAndEmpIdI.getDepartmentName())
                            .totalNum(faceCompareRecordTotalRespI.getTotalNum())
                            .normalNum(faceCompareRecordTotalRespI.getAuditNum())
                            .abnormalNum(faceCompareRecordTotalRespI.getTotalNum() - faceCompareRecordTotalRespI.getAuditNum())
                            .treatmentRate(faceCompareRecordTotalRespI.getAuditRatio().intValue())
                            .createTime(LocalDateTime.now())
                            .build()
            );

            if (faceCompareRecordTotalRespI.getTotalNum() == 0
                    || faceCompareRecordTotalRespI.getAuditRatio().compareTo(BigDecimal.valueOf(90)) >= 0
                    || departmentIdAndEmpIdI.getDepartmentId().equals(376)
                    || departmentIdAndEmpIdI.getEmployeeId().equals(2012)
            ) {
                //审核数大于等于90则不罚款
                return;
            }
            DeductionParam deductionParamI = new DeductionParam();
            deductionParamI.setDateFlag(endDateTime.toLocalDate().toString());
            deductionParamI.setAmount(BigDecimal.valueOf(50));
            deductionParamI.setEmpId(departmentIdAndEmpIdI.getEmployeeId());
            String reasonI = "20:00前小号的审核数未达到90%(截止到" + LocalDate.now() + "凌晨1点)" + "：需处理" + faceCompareRecordTotalRespI.getTotalNum() + "，实际处理" + faceCompareRecordTotalRespI.getAuditNum() + "，处理率" + faceCompareRecordTotalRespI.getAuditRatio() + "%";
            deductionParamI.setReason(reasonI);
            deductionParamList.add(deductionParamI);
        });
        if (CollUtil.isNotEmpty(trumpetTreatmentStatisticList)) {
            trumpetTreatmentStatisticService.saveBatch(trumpetTreatmentStatisticList);
        }

        if (CollUtil.isNotEmpty(deductionParamList)) {
            oaApiService.setDeduction(deductionParamList);
        }

    }


}
