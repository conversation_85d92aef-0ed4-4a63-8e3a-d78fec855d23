package quxing.data.portal.service.whole.impl;

import cn.hutool.core.collection.ListUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import quxing.data.portal.mapper.whole.WholeFansAgentRewardDao;
import quxing.data.portal.model.whole.WholeFansAgentReward;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class WholeFansAgentRewardService {

    private final WholeFansAgentRewardDao fansAgentRewardDao;
    private final WholeLiveRewardListService wholeLiveRewardListService;
    @Qualifier("scheduledAsyncExecutor")
    private final ThreadPoolTaskExecutor scheduledAsyncExecutor;


//   @Scheduled(fixedDelay = 120000)
    public void syncRewardNearly() {
        scheduledAsyncExecutor.execute(() -> {
            LocalDate now = LocalDate.now();
            Integer hour = LocalDateTime.now().getHour();
            String rewardTableName = wholeLiveRewardListService.getLiveRewardTableName(now);
            List<WholeFansAgentReward> list = fansAgentRewardDao.syncRewardNearly(rewardTableName, now.getDayOfMonth(), hour);
            list = Optional.ofNullable(list).orElse(new ArrayList<>());
            ListUtil.split(list, 500).forEach(item -> {
                fansAgentRewardDao.insertOrUpdate(item, hour, now.toString());
            });
        });
    }
}
