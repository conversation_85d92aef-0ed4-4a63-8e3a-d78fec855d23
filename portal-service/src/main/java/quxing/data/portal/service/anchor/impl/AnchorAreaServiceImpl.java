package quxing.data.portal.service.anchor.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import quxing.data.portal.mapper.anchor.AnchorAreaDao;
import quxing.data.portal.mapper.anchor.AreaDao;
import quxing.data.portal.model.anchor.AnchorAreaEntity;
import quxing.data.portal.model.anchor.Area;
import quxing.data.portal.model.anchor.Req.AnchorAreaPageReq;
import quxing.data.portal.model.anchor.Req.AnchorAreaReq;
import quxing.data.portal.model.anchor.Resp.AnchorAreaThreadCountResp;
import quxing.data.portal.service.anchor.AnchorAreaService;
import quxing.data.portal.service.anchor.AreaService;
import quxing.data.portal.utils.PageHelperUtil;
import quxing.data.portal.utils.PageUtil;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service("anchorAreaService")
public class AnchorAreaServiceImpl extends ServiceImpl<AnchorAreaDao, AnchorAreaEntity> implements AnchorAreaService {
    private final AreaService areaService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean alter(AnchorAreaReq anchorAreaReq) {
        Long anchorId = anchorAreaReq.getAnchorId();
        this.remove(
                new QueryWrapper<AnchorAreaEntity>()
                        .eq("anchor_id", anchorId)
        );
        List<Integer> areaIds = anchorAreaReq.getAreaIds();
        if (CollUtil.isEmpty(areaIds)) {
            return true;
        }
        Map<Integer, String> areaNameForId = Optional.ofNullable(areaService.listAll()).orElse(new LinkedList<>()).stream()
                .collect(Collectors.toMap(t -> t.getId().intValue(), Area::getName));
        List<AnchorAreaEntity> anchorAreaList = new LinkedList<>();
        for (Integer areaIdI : areaIds) {
            String areaNameI = Optional.ofNullable(areaNameForId.get(areaIdI)).orElse("");
            anchorAreaList.add(
                    AnchorAreaEntity.builder()
                            .anchorId(anchorId)
                            .areaId(areaIdI)
                            .areaName(areaNameI)
                            .build()
            );
        }
        boolean saveBatch = this.saveBatch(anchorAreaList);
        if (!saveBatch) {
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Override
    public PageUtil getPage(AnchorAreaPageReq pageReq) {
        QueryWrapper<AnchorAreaEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(pageReq.getAreaName()), "area_name", pageReq.getAreaName());


        PageHelperUtil.startPage(pageReq.getPage(), pageReq.getSize(), pageReq.getSortField(), pageReq.getIsAsc());
        List<AnchorAreaThreadCountResp> anchorAreaList = this.baseMapper.selectAreaThreadCount(pageReq.getAreaName());
        if (CollUtil.isEmpty(anchorAreaList)) {
            return new PageUtil(pageReq.getSize(), pageReq.getPage());
        }
        return new PageUtil(new PageInfo<>(anchorAreaList));
    }
}