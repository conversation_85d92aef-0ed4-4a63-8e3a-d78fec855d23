package quxing.data.portal.service.anchor.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import quxing.data.portal.common.util.ExcelUtils;
import quxing.data.portal.mapper.anchor.AnchorPublicImportDao;
import quxing.data.portal.model.anchor.AnchorBaseInfoEntity;
import quxing.data.portal.model.anchor.AnchorPublicImportEntity;
import quxing.data.portal.model.anchor.Req.AnchorPublicImportPageReq;
import quxing.data.portal.model.anchor.Resp.AnchorPublicImportPageResp;
import quxing.data.portal.model.anchor.dto.AnchorPublicImportExcelDto;
import quxing.data.portal.service.anchor.AnchorBaseInfoService;
import quxing.data.portal.service.anchor.AnchorPublicImportService;
import quxing.data.portal.utils.PageHelperUtil;
import quxing.data.portal.utils.PageUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AnchorPublicImportServiceImpl extends ServiceImpl<AnchorPublicImportDao, AnchorPublicImportEntity> implements AnchorPublicImportService {

    private final AnchorBaseInfoService anchorBaseInfoService;

    @Override
    public String importData(MultipartFile file) {
        try {
            // 1. 读取Excel文件
            List<AnchorPublicImportExcelDto> excelDataList = ExcelUtils.importExcel(file, AnchorPublicImportExcelDto.class);

            if (CollUtil.isEmpty(excelDataList)) {
                return "Excel文件为空或格式不正确";
            }

            // 2. 数据验证和去重（按原抖音号去重）
            List<AnchorPublicImportExcelDto> validDataList = new ArrayList<>();
            List<String> tiktokIdList = new ArrayList<>();

            for (AnchorPublicImportExcelDto dto : excelDataList) {
                // 验证必填字段
                if (StrUtil.isBlank(dto.getTiktokId())) {
                    continue;
                }

                // 按原抖音号去重
                if (!tiktokIdList.contains(dto.getTiktokId())) {
                    tiktokIdList.add(dto.getTiktokId());
                    validDataList.add(dto);
                }
            }

            if (CollUtil.isEmpty(validDataList)) {
                return "没有有效的数据可以导入";
            }

            // 3. 通过原抖音号获取主播ID
            List<Long> tikTokIds = validDataList.stream()
                    .map(dto -> {
                        try {
                            return Long.parseLong(dto.getTiktokId());
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(tikTokIds)) {
                return "原抖音号格式不正确，无法转换为数字";
            }

            // 查询anchor_base_info表获取主播信息
            List<AnchorBaseInfoEntity> anchorBaseInfoList = anchorBaseInfoService.list(
                    new QueryWrapper<AnchorBaseInfoEntity>()
                            .select("anchor_id", "tik_tok_id", "tik_tok_num", "nickname")
                            .in("tik_tok_id", tikTokIds)
            );

            // 创建原抖音号到主播ID的映射
            Map<Long, Long> tikTokIdToAnchorIdMap = anchorBaseInfoList.stream()
                    .collect(Collectors.toMap(
                            AnchorBaseInfoEntity::getTikTokId,
                            AnchorBaseInfoEntity::getAnchorId,
                            (existing, replacement) -> existing
                    ));

            // 4. 构建要插入的数据
            List<AnchorPublicImportEntity> insertList = new ArrayList<>();
            int successCount = 0;

            for (AnchorPublicImportExcelDto dto : validDataList) {
                try {
                    Long tikTokId = Long.parseLong(dto.getTiktokId());
                    Long anchorId = tikTokIdToAnchorIdMap.get(tikTokId);
                    AnchorPublicImportEntity entity = new AnchorPublicImportEntity();
                    if (anchorId != null) {
                        entity.setAnchorId(anchorId);
                    }
                    entity.setTiktokId(tikTokId);
                    entity.setTiktokNum(dto.getTiktokNum());
                    entity.setNickname(dto.getNickname());
                    entity.setCreateTime(LocalDateTime.now());
                    insertList.add(entity);
                    successCount++;
                } catch (NumberFormatException e) {
                    // 忽略格式错误的数据
                }
            }

            if (CollUtil.isEmpty(insertList)) {
                return "没有找到匹配的主播信息，无法导入";
            }

            // 5. 批量插入数据库（使用ON DUPLICATE KEY UPDATE处理重复数据）
            int insertCount = this.baseMapper.insertOrUpdateBatch(insertList);

            if (insertCount > 0) {
                return String.format("导入成功！共处理 %d 条数据，成功导入 %d 条", validDataList.size(), successCount);
            } else {
                return "数据保存失败";
            }

        } catch (Exception e) {
            return "导入失败：" + e.getMessage();
        }
    }

    @Override
    public PageUtil getPageList(AnchorPublicImportPageReq req) {
        // 启用分页和排序
        PageHelperUtil.startPage(
                req.getPage(),
                req.getSize(),
                req.getSortField(),
                req.getIsAsc()
        );

        // 查询数据
        List<AnchorPublicImportPageResp> list = this.baseMapper.selectPageList(req);

        if (CollUtil.isEmpty(list)) {
            return new PageUtil(req.getSize(), req.getPage());
        }

        return new PageUtil(new com.github.pagehelper.PageInfo<>(list));
    }
}
