package quxing.data.portal.service.face.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import quxing.data.portal.mapper.face.QueryFaceDetailDao;
import quxing.data.portal.model.face.QueryFaceDetailEntity;
import quxing.data.portal.service.face.QueryFaceDetailService;

/**
 * <AUTHOR>
 */
@Service("queryFaceDetailService")
public class QueryFaceDetailServiceImpl extends ServiceImpl<QueryFaceDetailDao, QueryFaceDetailEntity> implements QueryFaceDetailService {

}