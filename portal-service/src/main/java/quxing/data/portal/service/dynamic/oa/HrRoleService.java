package quxing.data.portal.service.dynamic.oa;

import com.baomidou.mybatisplus.extension.service.IService;
import quxing.data.portal.model.oa.user.HrRoleEntity;
import quxing.data.portal.model.oa.user.RoleEntity;
import quxing.data.portal.model.sys.Resp.SysUserRoleResp;

import java.util.List;

/**
 * 用户角色表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-10-07 15:49:30
 */
public interface HrRoleService extends IService<HrRoleEntity> {

    /**
     * 根据用户id获取用户的角色列表
     * @param hrId 用户id
     * @return 角色列表
     */
    List<RoleEntity> listByHr(Integer hrId);

    /**
     * 根据用户名获取用户和角色
     * @param username 用户名
     * @return 用户及角色
     */
    SysUserRoleResp getUserRolesByUser(String username);
}

